<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>招标采集平台 - API接口文档</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css" rel="stylesheet">
    <style>
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
            overflow-y: auto;
            padding-top: 20px;
        }
        .content {
            margin-left: 250px;
            padding: 20px;
        }
        .api-endpoint {
            background-color: #f8f9fa;
            border-left: 4px solid #0d6efd;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 5px 5px 0;
        }
        .method-get { border-left-color: #198754; }
        .method-post { border-left-color: #0d6efd; }
        .method-put { border-left-color: #fd7e14; }
        .method-delete { border-left-color: #dc3545; }
        .method-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-weight: bold;
        }
        .nav-link {
            color: #495057;
            padding: 0.5rem 1rem;
        }
        .nav-link:hover {
            background-color: #e9ecef;
        }
        .nav-link.active {
            background-color: #0d6efd;
            color: white;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: relative;
                width: 100%;
                height: auto;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <!-- 侧边栏导航 -->
    <div class="sidebar">
        <div class="px-3">
            <h5 class="text-primary">
                <i class="bi bi-book"></i> API文档
            </h5>
            <nav class="nav flex-column">
                <a class="nav-link active" href="#overview">概览</a>
                <a class="nav-link" href="#auth">认证</a>
                <a class="nav-link" href="#system">系统接口</a>
                <a class="nav-link" href="#sites">招标站点</a>
                <a class="nav-link" href="#rules">采集规则</a>
                <a class="nav-link" href="#tasks">采集任务</a>
                <a class="nav-link" href="#results">采集结果</a>
                <a class="nav-link" href="#test">测试接口</a>
                <a class="nav-link" href="#errors">错误码</a>
            </nav>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="content">
        <div class="container-fluid">
            <!-- 概览 -->
            <section id="overview">
                <h1>招标采集平台 API 文档</h1>
                <p class="lead">本文档描述了招标采集平台的所有API接口，包括请求格式、响应格式和示例代码。</p>
                
                <div class="alert alert-info">
                    <h6><i class="bi bi-info-circle"></i> 基础信息</h6>
                    <ul class="mb-0">
                        <li><strong>基础URL:</strong> <code>http://localhost:5000/api</code></li>
                        <li><strong>数据格式:</strong> JSON</li>
                        <li><strong>字符编码:</strong> UTF-8</li>
                        <li><strong>请求头:</strong> <code>Content-Type: application/json</code></li>
                    </ul>
                </div>

                <h3>通用响应格式</h3>
                <pre><code class="language-json">{
    "code": 200,
    "message": "success",
    "data": {}
}</code></pre>
            </section>

            <!-- 系统接口 -->
            <section id="system" class="mt-5">
                <h2>系统接口</h2>
                
                <div class="api-endpoint method-get">
                    <h5>
                        <span class="badge bg-success method-badge">GET</span>
                        健康检查
                    </h5>
                    <p><code>/api/health</code></p>
                    <p>检查API服务状态</p>
                    
                    <h6>响应示例:</h6>
                    <pre><code class="language-json">{
    "code": 200,
    "message": "API服务正常运行",
    "data": {
        "status": "healthy",
        "version": "1.0.0"
    }
}</code></pre>
                </div>

                <div class="api-endpoint method-get">
                    <h5>
                        <span class="badge bg-success method-badge">GET</span>
                        获取统计数据
                    </h5>
                    <p><code>/api/statistics</code></p>
                    <p>获取首页统计信息</p>
                    
                    <h6>响应示例:</h6>
                    <pre><code class="language-json">{
    "code": 200,
    "message": "success",
    "data": {
        "total_sites": 10,
        "today_collected": 5,
        "today_uncollected": 5,
        "today_success": 4,
        "today_failed": 1
    }
}</code></pre>
                </div>
            </section>

            <!-- 招标站点接口 -->
            <section id="sites" class="mt-5">
                <h2>招标站点接口</h2>
                
                <div class="api-endpoint method-get">
                    <h5>
                        <span class="badge bg-success method-badge">GET</span>
                        获取站点列表
                    </h5>
                    <p><code>/api/bid_sites</code></p>
                    <p>获取招标站点列表（分页）</p>
                    
                    <h6>查询参数:</h6>
                    <ul>
                        <li><code>page</code> - 页码（默认1）</li>
                        <li><code>page_size</code> - 每页数量（默认20）</li>
                        <li><code>site_name</code> - 站点名称筛选</li>
                        <li><code>province</code> - 省份筛选</li>
                        <li><code>city</code> - 城市筛选</li>
                        <li><code>is_test</code> - 是否测试规则（0/1）</li>
                    </ul>
                </div>

                <div class="api-endpoint method-post">
                    <h5>
                        <span class="badge bg-primary method-badge">POST</span>
                        创建站点
                    </h5>
                    <p><code>/api/bid_sites</code></p>
                    <p>创建新的招标站点</p>
                    
                    <h6>请求体示例:</h6>
                    <pre><code class="language-json">{
    "site_name": "站点名称",
    "site_url": "http://example.com",
    "province": "北京",
    "city": "北京",
    "added_by": "管理员",
    "is_test": 0,
    "collect_keywords": "关键词1,关键词2"
}</code></pre>
                </div>
            </section>

            <!-- 测试接口 -->
            <section id="test" class="mt-5">
                <h2>测试接口</h2>
                
                <div class="api-endpoint method-post">
                    <h5>
                        <span class="badge bg-primary method-badge">POST</span>
                        测试链接提取
                    </h5>
                    <p><code>/api/spider_test/links</code></p>
                    <p>测试链接提取规则</p>
                    
                    <h6>请求体示例:</h6>
                    <pre><code class="language-json">{
    "url": "http://example.com",
    "extract_type": "re",
    "extract_rule": "href=\"([^\"]*)\""
}</code></pre>
                </div>

                <div class="api-endpoint method-post">
                    <h5>
                        <span class="badge bg-primary method-badge">POST</span>
                        测试完整流程
                    </h5>
                    <p><code>/api/spider_test/full</code></p>
                    <p>测试完整采集流程</p>
                    
                    <h6>请求体示例:</h6>
                    <pre><code class="language-json">{
    "list_url": "http://example.com/list.html",
    "link_extract_type": "re",
    "link_extract_rule": "href=\"([^\"]*\\.html?)\"",
    "title_extract_type": "xpath",
    "title_extract_rule": "//title/text()",
    "content_extract_type": "bs4",
    "content_extract_rule": "div.content",
    "max_links": 5
}</code></pre>
                </div>
            </section>

            <!-- 错误码 -->
            <section id="errors" class="mt-5">
                <h2>错误码说明</h2>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>错误码</th>
                                <th>说明</th>
                                <th>示例</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>200</code></td>
                                <td>成功</td>
                                <td>请求处理成功</td>
                            </tr>
                            <tr>
                                <td><code>400</code></td>
                                <td>请求参数错误</td>
                                <td>缺少必填字段</td>
                            </tr>
                            <tr>
                                <td><code>404</code></td>
                                <td>资源不存在</td>
                                <td>站点ID不存在</td>
                            </tr>
                            <tr>
                                <td><code>500</code></td>
                                <td>服务器内部错误</td>
                                <td>数据库连接失败</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- 返回首页 -->
            <div class="mt-5 text-center">
                <a href="/" class="btn btn-primary">
                    <i class="bi bi-house"></i> 返回首页
                </a>
                <a href="/api/info" class="btn btn-outline-secondary" target="_blank">
                    <i class="bi bi-info-circle"></i> API信息
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/plugins/autoloader/prism-autoloader.min.js"></script>
    <script>
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });

        // 高亮当前导航
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('section');
            const navLinks = document.querySelectorAll('.nav-link');
            
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                if (pageYOffset >= sectionTop - 60) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
