from flask import Blueprint, request, jsonify
from models.collection_result import CollectionResult

collection_results_bp = Blueprint('collection_results', __name__)

@collection_results_bp.route('', methods=['GET'])
def get_collection_results():
    """获取采集结果列表"""
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        
        # 获取筛选条件
        filters = {}
        if request.args.get('site_name'):
            filters['site_name'] = request.args.get('site_name')
        if request.args.get('province'):
            filters['province'] = request.args.get('province')
        if request.args.get('city'):
            filters['city'] = request.args.get('city')
        if request.args.get('publish_status'):
            filters['publish_status'] = request.args.get('publish_status')
        if request.args.get('start_date'):
            filters['start_date'] = request.args.get('start_date')
        if request.args.get('end_date'):
            filters['end_date'] = request.args.get('end_date')
        if request.args.get('keyword'):
            filters['keyword'] = request.args.get('keyword')
        
        result = CollectionResult.get_all(page, page_size, filters)
        
        return jsonify({
            'code': 200,
            'message': 'success',
            'data': result
        })
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'获取采集结果失败: {str(e)}'
        })

@collection_results_bp.route('/<int:result_id>', methods=['GET'])
def get_collection_result(result_id):
    """获取单个采集结果详情"""
    try:
        result = CollectionResult.get_by_id(result_id)
        if not result:
            return jsonify({
                'code': 404,
                'message': '采集结果不存在'
            })
        
        return jsonify({
            'code': 200,
            'message': 'success',
            'data': result
        })
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'获取采集结果详情失败: {str(e)}'
        })

@collection_results_bp.route('/task/<int:task_id>', methods=['GET'])
def get_task_results(task_id):
    """获取任务的采集结果"""
    try:
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        
        result = CollectionResult.get_by_task_id(task_id, page, page_size)
        
        return jsonify({
            'code': 200,
            'message': 'success',
            'data': result
        })
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'获取任务采集结果失败: {str(e)}'
        })

@collection_results_bp.route('/<int:result_id>/publish', methods=['PUT'])
def update_publish_status(result_id):
    """更新发布状态"""
    try:
        data = request.get_json()
        publish_status = data.get('publish_status')
        
        if not publish_status:
            return jsonify({
                'code': 400,
                'message': '缺少发布状态参数'
            })
        
        valid_statuses = ['unpublished', 'published']
        if publish_status not in valid_statuses:
            return jsonify({
                'code': 400,
                'message': f'发布状态必须是 {valid_statuses} 中的一个'
            })
        
        result = CollectionResult.update_publish_status(result_id, publish_status)
        if result:
            return jsonify({
                'code': 200,
                'message': '发布状态更新成功'
            })
        else:
            return jsonify({
                'code': 500,
                'message': '发布状态更新失败'
            })
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'更新发布状态失败: {str(e)}'
        })

@collection_results_bp.route('/batch/publish', methods=['PUT'])
def batch_update_publish_status():
    """批量更新发布状态"""
    try:
        data = request.get_json()
        result_ids = data.get('result_ids', [])
        publish_status = data.get('publish_status')
        
        if not result_ids or not publish_status:
            return jsonify({
                'code': 400,
                'message': '缺少必要参数'
            })
        
        valid_statuses = ['unpublished', 'published']
        if publish_status not in valid_statuses:
            return jsonify({
                'code': 400,
                'message': f'发布状态必须是 {valid_statuses} 中的一个'
            })
        
        success_count = 0
        for result_id in result_ids:
            if CollectionResult.update_publish_status(result_id, publish_status):
                success_count += 1
        
        return jsonify({
            'code': 200,
            'message': f'批量更新完成，成功更新 {success_count} 条记录'
        })
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'批量更新发布状态失败: {str(e)}'
        })
