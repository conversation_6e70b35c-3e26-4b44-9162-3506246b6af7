#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试添加站点功能
"""

import requests
import json

def test_add_site():
    """测试添加站点"""
    url = "http://localhost:5000/api/bid_sites"
    
    data = {
        "site_name": "测试站点",
        "site_url": "http://www.baidu.com",
        "province": "北京",
        "city": "北京",
        "added_by": "测试用户",
        "source_id": "test_001",
        "is_test": 1,
        "url_whitelist": "",
        "url_blacklist": "",
        "collect_keywords": "招标,采购",
        "block_keywords": "测试"
    }
    
    print("发送请求...")
    print(f"URL: {url}")
    print(f"数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(url, json=data, timeout=10)
        print(f"\n状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                print(f"\n✅ 创建成功！站点ID: {result.get('data', {}).get('id')}")
            else:
                print(f"\n❌ 创建失败: {result.get('message')}")
        else:
            print(f"\n❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"\n❌ 请求异常: {e}")

def test_get_sites():
    """测试获取站点列表"""
    url = "http://localhost:5000/api/bid_sites"
    
    print("\n" + "="*50)
    print("测试获取站点列表")
    print("="*50)
    
    try:
        response = requests.get(url, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                sites = result.get('data', {}).get('data', [])
                print(f"✅ 获取成功！共 {len(sites)} 个站点")
                for i, site in enumerate(sites, 1):
                    print(f"  {i}. {site.get('site_name')} - {site.get('site_url')}")
            else:
                print(f"❌ 获取失败: {result.get('message')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    print("="*50)
    print("测试添加招标站点")
    print("="*50)
    test_add_site()
    test_get_sites()
