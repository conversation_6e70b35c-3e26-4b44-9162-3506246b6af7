from flask import Blueprint, request, jsonify
from models.collection_rule import CollectionRule

collection_rules_bp = Blueprint('collection_rules', __name__)

@collection_rules_bp.route('/<int:site_id>', methods=['GET'])
def get_collection_rule(site_id):
    """获取采集规则"""
    try:
        rule = CollectionRule.get_by_site_id(site_id)
        if not rule:
            return jsonify({
                'code': 404,
                'message': '采集规则不存在'
            })
        
        return jsonify({
            'code': 200,
            'message': 'success',
            'data': rule
        })
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'获取采集规则失败: {str(e)}'
        })

@collection_rules_bp.route('/<int:site_id>', methods=['POST', 'PUT'])
def save_collection_rule(site_id):
    """保存采集规则"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = [
            'link_extract_type', 'link_extract_rule',
            'title_extract_type', 'title_extract_rule',
            'content_extract_type', 'content_extract_rule'
        ]
        
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'code': 400,
                    'message': f'缺少必填字段: {field}'
                })
        
        # 验证提取方式
        valid_types = ['re', 'xpath', 'bs4']
        for extract_type in ['link_extract_type', 'title_extract_type', 'content_extract_type']:
            if data.get(extract_type) not in valid_types:
                return jsonify({
                    'code': 400,
                    'message': f'{extract_type} 必须是 {valid_types} 中的一个'
                })
        
        result = CollectionRule.create_or_update(site_id, data)
        if result:
            return jsonify({
                'code': 200,
                'message': '保存成功'
            })
        else:
            return jsonify({
                'code': 500,
                'message': '保存失败'
            })
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'保存采集规则失败: {str(e)}'
        })

@collection_rules_bp.route('/<int:site_id>', methods=['DELETE'])
def delete_collection_rule(site_id):
    """删除采集规则"""
    try:
        result = CollectionRule.delete_by_site_id(site_id)
        if result:
            return jsonify({
                'code': 200,
                'message': '删除成功'
            })
        else:
            return jsonify({
                'code': 500,
                'message': '删除失败'
            })
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'删除采集规则失败: {str(e)}'
        })
