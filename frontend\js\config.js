// 配置文件
const CONFIG = {
    // API基础URL
    API_BASE_URL: 'http://localhost:5000/api',
    
    // 分页配置
    DEFAULT_PAGE_SIZE: 20,
    PAGE_SIZE_OPTIONS: [20, 50, 100],
    
    // 请求超时时间（毫秒）
    REQUEST_TIMEOUT: 30000,
    
    // 提取方式选项
    EXTRACT_TYPES: [
        { value: 're', label: '正则表达式 (re)' },
        { value: 'xpath', label: 'XPath' },
        { value: 'bs4', label: 'BeautifulSoup4' }
    ],
    
    // 省份选项
    PROVINCES: [
        '北京', '上海', '天津', '重庆',
        '河北', '山西', '辽宁', '吉林', '黑龙江',
        '江苏', '浙江', '安徽', '福建', '江西', '山东',
        '河南', '湖北', '湖南', '广东', '海南',
        '四川', '贵州', '云南', '陕西', '甘肃', '青海',
        '台湾', '内蒙古', '广西', '西藏', '宁夏', '新疆',
        '香港', '澳门'
    ],
    
    // 任务状态
    TASK_STATUS: {
        'pending': { label: '待执行', class: 'warning' },
        'running': { label: '执行中', class: 'info' },
        'completed': { label: '已完成', class: 'success' },
        'failed': { label: '失败', class: 'danger' }
    },
    
    // 发布状态
    PUBLISH_STATUS: {
        'unpublished': { label: '未发布', class: 'warning' },
        'published': { label: '已发布', class: 'success' }
    },
    
    // 消息类型
    MESSAGE_TYPES: {
        SUCCESS: 'success',
        ERROR: 'danger',
        WARNING: 'warning',
        INFO: 'info'
    }
};
