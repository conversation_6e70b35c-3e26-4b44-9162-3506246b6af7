# 招标采集平台 - 使用说明

## 🚀 快速启动

### 最简单的启动方式
**双击 `启动服务.bat` 文件即可启动！**

### 其他启动方式
```bash
# 方法二：命令行启动
python start.py

# 方法三：分别启动后端
python start_server.py
```

## 📱 访问地址

启动成功后，在浏览器中访问：
- **主页面**: http://localhost:5000
- **API文档**: http://localhost:5000/docs/api.html

## ✨ 主要功能

### 1. 📊 首页统计
- 查看总站点数、今日采集数据
- 快捷操作按钮
- API文档入口

### 2. 🏢 招标站点管理
- **添加站点**：点击"添加招标站点"按钮
- **信息类型**：选择"招标"或"拍卖"
- **状态管理**：设置"启用"或"禁用"
- **筛选功能**：
  - 按名称、URL搜索
  - 按省份、城市筛选
  - 按信息类型筛选（招标/拍卖）
  - 按状态筛选（启用/禁用）
  - 按测试状态筛选

### 3. 🔧 采集规则配置
- 为每个站点配置采集规则
- 支持正则表达式、XPath、CSS选择器
- 实时测试规则有效性

### 4. 🧪 测试功能
- 测试链接提取规则
- 测试内容提取规则
- 完整流程测试

## 🎯 筛选功能使用

### 实时搜索
- 在筛选条件中输入内容，自动搜索（500ms延迟）
- 支持站点名称、URL模糊搜索

### 多条件筛选
- 可以同时设置多个筛选条件
- 例如：筛选"北京"+"招标"+"启用"状态的站点

### 重置筛选
- 点击"重置"按钮清空所有筛选条件

## 📝 操作步骤

### 添加新站点
1. 点击"招标站点"菜单
2. 点击"添加招标站点"按钮
3. 填写站点信息：
   - 站点名称（必填）
   - 站点URL（必填）
   - 信息类型：选择"招标"或"拍卖"
   - 站点状态：选择"启用"或"禁用"
   - 省份、城市等其他信息
4. 点击"保存"

### 配置采集规则
1. 在站点列表中点击"配置规则"
2. 设置链接提取规则
3. 设置标题提取规则
4. 设置内容提取规则
5. 使用测试功能验证规则
6. 保存规则

### 查看采集结果
1. 点击"采集结果"菜单
2. 查看已采集的数据
3. 使用筛选功能查找特定结果

## 🔍 故障排除

### 启动失败
- 确保MySQL服务已启动
- 检查Python环境
- 确认端口5000未被占用

### 页面无法访问
- 检查后端服务是否正常启动
- 尝试访问 http://127.0.0.1:5000

### 数据库连接失败
- 检查.env文件中的数据库配置
- 运行 `python init_db.py` 重新初始化数据库

## 💡 使用技巧

1. **筛选技巧**：使用组合筛选快速找到目标站点
2. **测试规则**：添加规则后务必测试确保正确性
3. **状态管理**：使用禁用状态暂停不需要的站点
4. **分类管理**：使用信息类型区分招标和拍卖站点

---

**祝您使用愉快！** 🎉
