// API请求封装
class API {
    constructor() {
        this.baseURL = CONFIG.API_BASE_URL;
        this.timeout = CONFIG.REQUEST_TIMEOUT;
    }

    // 通用请求方法
    async request(url, options = {}) {
        const fullURL = url.startsWith('http') ? url : `${this.baseURL}${url}`;
        
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
            timeout: this.timeout
        };

        const finalOptions = { ...defaultOptions, ...options };

        try {
            const response = await fetch(fullURL, finalOptions);
            const data = await response.json();
            
            if (data.code === 200) {
                return { success: true, data: data.data, message: data.message };
            } else {
                return { success: false, message: data.message || '请求失败' };
            }
        } catch (error) {
            console.error('API请求错误:', error);
            return { success: false, message: '网络请求失败' };
        }
    }

    // GET请求
    async get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullURL = queryString ? `${url}?${queryString}` : url;
        
        return this.request(fullURL, {
            method: 'GET'
        });
    }

    // POST请求
    async post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // PUT请求
    async put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    // DELETE请求
    async delete(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    }

    // 获取统计数据
    async getStatistics() {
        return this.get('/statistics');
    }

    // 招标站点相关API
    async getBidSites(params = {}) {
        return this.get('/bid_sites', params);
    }

    async getBidSite(id) {
        return this.get(`/bid_sites/${id}`);
    }

    async createBidSite(data) {
        return this.post('/bid_sites', data);
    }

    async updateBidSite(id, data) {
        return this.put(`/bid_sites/${id}`, data);
    }

    async deleteBidSite(id) {
        return this.delete(`/bid_sites/${id}`);
    }

    // 采集规则相关API
    async getCollectionRule(siteId) {
        return this.get(`/collection_rules/${siteId}`);
    }

    async saveCollectionRule(siteId, data) {
        return this.post(`/collection_rules/${siteId}`, data);
    }

    async deleteCollectionRule(siteId) {
        return this.delete(`/collection_rules/${siteId}`);
    }

    // 采集任务相关API
    async createCollectionTask(data) {
        return this.post('/collection_tasks', data);
    }

    async getCollectionTask(id) {
        return this.get(`/collection_tasks/${id}`);
    }

    async getSiteCollectionTasks(siteId, params = {}) {
        return this.get(`/collection_tasks/site/${siteId}`, params);
    }

    async updateTaskStatus(id, data) {
        return this.put(`/collection_tasks/${id}/status`, data);
    }

    // 采集结果相关API
    async getCollectionResults(params = {}) {
        return this.get('/collection_results', params);
    }

    async getCollectionResult(id) {
        return this.get(`/collection_results/${id}`);
    }

    async getTaskResults(taskId, params = {}) {
        return this.get(`/collection_results/task/${taskId}`, params);
    }

    async updatePublishStatus(id, status) {
        return this.put(`/collection_results/${id}/publish`, { publish_status: status });
    }

    async batchUpdatePublishStatus(resultIds, status) {
        return this.put('/collection_results/batch/publish', {
            result_ids: resultIds,
            publish_status: status
        });
    }

    // 测试相关API
    async testLinkExtraction(data) {
        return this.post('/spider_test/links', data);
    }

    async testContentExtraction(data) {
        return this.post('/spider_test/content', data);
    }

    async testFullExtraction(data) {
        return this.post('/spider_test/full', data);
    }

    // 健康检查
    async healthCheck() {
        return this.get('/health');
    }
}

// 创建全局API实例
const api = new API();
