<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>招标采集平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#"><i class="bi bi-search"></i> 招标采集平台</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" onclick="showPage('dashboard')">
                            <i class="bi bi-house"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPage('sites')">
                            <i class="bi bi-list-ul"></i> 招标站点
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPage('results')">
                            <i class="bi bi-file-text"></i> 采集结果
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPage('test')">
                            <i class="bi bi-gear"></i> 规则测试
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container-fluid mt-4">
        <!-- 首页统计 -->
        <div id="dashboard-page" class="page-content">
            <div class="row">
                <div class="col-md-3">
                    <div class="card text-white bg-primary mb-3 stat-card">
                        <div class="card-header" style="color: white; font-weight: bold;">总招标站点数</div>
                        <div class="card-body">
                            <h4 class="card-title" id="total-sites" style="color: white; font-weight: bold;">加载中...</h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-success mb-3 stat-card">
                        <div class="card-header" style="color: white; font-weight: bold;">今日已采集</div>
                        <div class="card-body">
                            <h4 class="card-title" id="today-collected" style="color: white; font-weight: bold;">加载中...</h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-warning mb-3 stat-card">
                        <div class="card-header" style="color: white; font-weight: bold;">今日未采集</div>
                        <div class="card-body">
                            <h4 class="card-title" id="today-uncollected" style="color: white; font-weight: bold;">加载中...</h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-info mb-3 stat-card">
                        <div class="card-header" style="color: white; font-weight: bold;">今日成功率</div>
                        <div class="card-body">
                            <h4 class="card-title" id="success-rate" style="color: white; font-weight: bold;">加载中...</h4>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-lightning-charge"></i> 快捷操作</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6 col-md-12 mb-3">
                                    <h6 class="text-muted mb-2">核心功能</h6>
                                    <button class="btn btn-primary me-2 mb-2" onclick="showPage('sites', 'add')">
                                        <i class="bi bi-plus-circle"></i> 添加招标站点
                                    </button>
                                    <button class="btn btn-success me-2 mb-2" onclick="showPage('test')">
                                        <i class="bi bi-gear-fill"></i> 测试采集规则
                                    </button>
                                    <button class="btn btn-info me-2 mb-2" onclick="showPage('results')">
                                        <i class="bi bi-file-text-fill"></i> 查看采集结果
                                    </button>
                                </div>
                                <div class="col-lg-6 col-md-12 mb-3">
                                    <h6 class="text-muted mb-2">开发工具</h6>
                                    <button class="btn btn-outline-secondary me-2 mb-2" onclick="openApiDocs()">
                                        <i class="bi bi-book"></i> API接口文档
                                    </button>
                                    <button class="btn btn-outline-secondary me-2 mb-2" onclick="openApiInfo()">
                                        <i class="bi bi-info-circle"></i> API信息
                                    </button>
                                    <button class="btn btn-outline-secondary me-2 mb-2" onclick="checkHealth()">
                                        <i class="bi bi-heart-pulse"></i> 系统状态
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 招标站点页面 -->
        <div id="sites-page" class="page-content" style="display: none;">
            <!-- 筛选条件 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="bi bi-funnel"></i> 筛选条件</h6>
                </div>
                <div class="card-body filter-section">
                    <div class="row g-2">
                        <div class="col-lg-2 col-md-3 col-sm-6">
                            <input type="text" class="form-control form-control-sm" id="filter-site-name" placeholder="站点名称">
                        </div>
                        <div class="col-lg-2 col-md-3 col-sm-6">
                            <input type="text" class="form-control form-control-sm" id="filter-site-url" placeholder="站点URL">
                        </div>
                        <div class="col-lg-2 col-md-3 col-sm-6">
                            <select class="form-select form-select-sm" id="filter-province">
                                <option value="">选择省份</option>
                                <option value="北京">北京</option>
                                <option value="上海">上海</option>
                                <option value="广东">广东</option>
                                <option value="浙江">浙江</option>
                                <option value="江苏">江苏</option>
                                <option value="全国">全国</option>
                            </select>
                        </div>
                        <div class="col-lg-2 col-md-3 col-sm-6">
                            <input type="text" class="form-control form-control-sm" id="filter-city" placeholder="城市">
                        </div>
                        <div class="col-lg-2 col-md-3 col-sm-6">
                            <select class="form-select form-select-sm" id="filter-info-type">
                                <option value="">信息类型</option>
                                <option value="招标">招标</option>
                                <option value="拍卖">拍卖</option>
                            </select>
                        </div>
                        <div class="col-lg-2 col-md-3 col-sm-6">
                            <select class="form-select form-select-sm" id="filter-status">
                                <option value="">站点状态</option>
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                    </div>
                    <div class="row g-2 mt-1">
                        <div class="col-lg-2 col-md-6 col-sm-6">
                            <select class="form-select form-select-sm" id="filter-is-test">
                                <option value="">测试状态</option>
                                <option value="0">正式规则</option>
                                <option value="1">测试规则</option>
                            </select>
                        </div>
                        <div class="col-lg-4 col-md-6 col-sm-12">
                            <div class="filter-buttons">
                                <button class="btn btn-primary btn-sm" onclick="app.loadSites()">
                                    <i class="bi bi-search"></i> <span class="d-none d-sm-inline">搜索</span>
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="app.resetFilters()">
                                    <i class="bi bi-arrow-clockwise"></i> <span class="d-none d-sm-inline">重置</span>
                                </button>
                                <button class="btn btn-success btn-sm flex-fill" onclick="app.showSiteForm()">
                                    <i class="bi bi-plus"></i> <span class="d-none d-sm-inline">添加站点</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 站点列表 -->
            <div class="card">
                <div class="card-header">
                    <h5>招标站点列表</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th style="min-width: 120px;">站点名称</th>
                                    <th class="d-none d-md-table-cell" style="min-width: 200px;">网址</th>
                                    <th class="d-none d-lg-table-cell">省份</th>
                                    <th class="d-none d-lg-table-cell">城市</th>
                                    <th class="d-none d-xl-table-cell">添加人</th>
                                    <th style="min-width: 80px;">信息类型</th>
                                    <th style="min-width: 60px;">状态</th>
                                    <th style="min-width: 60px;">测试</th>
                                    <th class="d-none d-lg-table-cell" style="min-width: 140px;">最新运行</th>
                                    <th class="d-none d-md-table-cell" style="min-width: 120px;">创建时间</th>
                                    <th style="min-width: 120px;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="sites-table-body">
                                <!-- 动态加载 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <nav aria-label="分页导航">
                        <ul class="pagination justify-content-center" id="sites-pagination">
                            <!-- 动态生成 -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>

        <!-- 采集结果页面 -->
        <div id="results-page" class="page-content" style="display: none;">
            <!-- 筛选条件 -->
            <div class="card mb-3">
                <div class="card-body">
                    <div class="row g-2">
                        <div class="col-lg-2 col-md-3 col-sm-6">
                            <input type="text" class="form-control form-control-sm" id="filter-result-site" placeholder="站点名称">
                        </div>
                        <div class="col-lg-2 col-md-3 col-sm-6">
                            <select class="form-select form-select-sm" id="filter-result-province">
                                <option value="">选择省份</option>
                                <option value="北京">北京</option>
                                <option value="上海">上海</option>
                                <option value="广东">广东</option>
                            </select>
                        </div>
                        <div class="col-lg-2 col-md-3 col-sm-6">
                            <select class="form-select form-select-sm" id="filter-publish-status">
                                <option value="">发布状态</option>
                                <option value="unpublished">未发布</option>
                                <option value="published">已发布</option>
                            </select>
                        </div>
                        <div class="col-lg-2 col-md-3 col-sm-6">
                            <input type="date" class="form-control form-control-sm" id="filter-start-date" title="开始日期">
                        </div>
                        <div class="col-lg-2 col-md-6 col-sm-6">
                            <input type="date" class="form-control form-control-sm" id="filter-end-date" title="结束日期">
                        </div>
                        <div class="col-lg-2 col-md-6 col-sm-6">
                            <button class="btn btn-primary btn-sm w-100" onclick="app.loadResults()">
                                <i class="bi bi-search"></i> <span class="d-none d-sm-inline">搜索</span>
                            </button>
                        </div>
                    </div>
                    <div class="row g-2 mt-1">
                        <div class="col-md-6 col-sm-8">
                            <input type="text" class="form-control form-control-sm" id="filter-keyword" placeholder="标题或内容关键词">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 结果列表 -->
            <div class="card">
                <div class="card-header">
                    <h5>采集结果列表</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th style="min-width: 120px;">站点名称</th>
                                    <th style="min-width: 200px;">招标标题</th>
                                    <th class="d-none d-lg-table-cell">省份</th>
                                    <th style="min-width: 80px;">发布状态</th>
                                    <th class="d-none d-md-table-cell" style="min-width: 120px;">采集时间</th>
                                    <th style="min-width: 100px;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="results-table-body">
                                <!-- 动态加载 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <nav aria-label="分页导航">
                        <ul class="pagination justify-content-center" id="results-pagination">
                            <!-- 动态生成 -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>

        <!-- 规则测试页面 -->
        <div id="test-page" class="page-content" style="display: none;">
            <div class="row">
                <!-- 链接提取测试 -->
                <div class="col-lg-6 col-md-12 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5><i class="bi bi-link"></i> 链接提取测试</h5>
                        </div>
                        <div class="card-body">
                            <form id="link-test-form">
                                <div class="mb-3">
                                    <label class="form-label">测试URL</label>
                                    <input type="url" class="form-control" id="link-test-url"
                                           placeholder="http://example.com" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">提取方式</label>
                                    <select class="form-select" id="link-extract-type" required>
                                        <option value="">选择提取方式</option>
                                        <option value="re">正则表达式 (re)</option>
                                        <option value="xpath">XPath</option>
                                        <option value="bs4">BeautifulSoup4</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">提取规则</label>
                                    <textarea class="form-control" id="link-extract-rule" rows="3"
                                              placeholder="请输入提取规则" required></textarea>
                                    <div class="form-text">
                                        <small>
                                            正则示例: href="([^"]*\.html?[^"]*)"<br>
                                            XPath示例: //a/@href<br>
                                            BS4示例: css:a[href]
                                        </small>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-play"></i> 测试链接提取
                                </button>
                            </form>

                            <div id="link-test-result" class="mt-3" style="display: none;">
                                <!-- 测试结果 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 内容提取测试 -->
                <div class="col-lg-6 col-md-12 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5><i class="bi bi-file-text"></i> 内容提取测试</h5>
                        </div>
                        <div class="card-body">
                            <form id="content-test-form">
                                <div class="mb-3">
                                    <label class="form-label">测试URL</label>
                                    <input type="url" class="form-control" id="content-test-url"
                                           placeholder="http://example.com/detail.html" required>
                                </div>

                                <div class="rule-form-section">
                                    <h6>标题提取规则</h6>
                                    <div class="mb-3">
                                        <label class="form-label">提取方式</label>
                                        <select class="form-select" id="title-extract-type" required>
                                            <option value="">选择提取方式</option>
                                            <option value="re">正则表达式 (re)</option>
                                            <option value="xpath">XPath</option>
                                            <option value="bs4">BeautifulSoup4</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">提取规则</label>
                                        <textarea class="form-control" id="title-extract-rule" rows="2"
                                                  placeholder="标题提取规则" required></textarea>
                                    </div>
                                </div>

                                <div class="rule-form-section">
                                    <h6>内容提取规则</h6>
                                    <div class="mb-3">
                                        <label class="form-label">提取方式</label>
                                        <select class="form-select" id="content-extract-type" required>
                                            <option value="">选择提取方式</option>
                                            <option value="re">正则表达式 (re)</option>
                                            <option value="xpath">XPath</option>
                                            <option value="bs4">BeautifulSoup4</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">提取规则</label>
                                        <textarea class="form-control" id="content-extract-rule" rows="2"
                                                  placeholder="内容提取规则" required></textarea>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-play"></i> 测试内容提取
                                </button>
                            </form>

                            <div id="content-test-result" class="mt-3" style="display: none;">
                                <!-- 测试结果 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 完整流程测试 -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-gear"></i> 完整采集流程测试</h5>
                        </div>
                        <div class="card-body">
                            <form id="full-test-form">
                                <div class="row g-3">
                                    <div class="col-lg-6 col-md-12">
                                        <div class="mb-3">
                                            <label class="form-label">列表页URL</label>
                                            <input type="url" class="form-control form-control-sm" id="full-test-list-url"
                                                   placeholder="http://example.com/list.html" required>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">最大测试链接数</label>
                                            <select class="form-select form-select-sm" id="full-test-max-links">
                                                <option value="3">3个链接</option>
                                                <option value="5" selected>5个链接</option>
                                                <option value="10">10个链接</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 col-md-12">
                                        <div class="mb-3">
                                            <label class="form-label">链接提取方式</label>
                                            <select class="form-select form-select-sm" id="full-link-extract-type" required>
                                                <option value="">选择提取方式</option>
                                                <option value="re">正则表达式 (re)</option>
                                                <option value="xpath">XPath</option>
                                                <option value="bs4">BeautifulSoup4</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">链接提取规则</label>
                                            <textarea class="form-control form-control-sm" id="full-link-extract-rule" rows="2"
                                                      placeholder="链接提取规则" required></textarea>
                                        </div>
                                    </div>
                                </div>

                                <div class="row g-3">
                                    <div class="col-lg-6 col-md-12">
                                        <div class="rule-form-section">
                                            <h6>标题提取规则</h6>
                                            <div class="mb-3">
                                                <select class="form-select form-select-sm" id="full-title-extract-type" required>
                                                    <option value="">选择提取方式</option>
                                                    <option value="re">正则表达式 (re)</option>
                                                    <option value="xpath">XPath</option>
                                                    <option value="bs4">BeautifulSoup4</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <textarea class="form-control form-control-sm" id="full-title-extract-rule" rows="2"
                                                          placeholder="标题提取规则" required></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 col-md-12">
                                        <div class="rule-form-section">
                                            <h6>内容提取规则</h6>
                                            <div class="mb-3">
                                                <select class="form-select form-select-sm" id="full-content-extract-type" required>
                                                    <option value="">选择提取方式</option>
                                                    <option value="re">正则表达式 (re)</option>
                                                    <option value="xpath">XPath</option>
                                                    <option value="bs4">BeautifulSoup4</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <textarea class="form-control form-control-sm" id="full-content-extract-rule" rows="2"
                                                          placeholder="内容提取规则" required></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="bi bi-play-circle"></i> 开始完整测试
                                </button>
                            </form>

                            <div id="full-test-result" class="mt-4" style="display: none;">
                                <!-- 完整测试结果 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 站点添加/编辑模态框 -->
    <div class="modal fade" id="siteModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="siteModalTitle">添加招标站点</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="siteForm">
                        <input type="hidden" id="siteId">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">站点名称 *</label>
                                    <input type="text" class="form-control" id="siteName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">站点URL *</label>
                                    <input type="url" class="form-control" id="siteUrl" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">省份</label>
                                    <select class="form-select" id="siteProvince">
                                        <option value="">选择省份</option>
                                        <option value="北京">北京</option>
                                        <option value="上海">上海</option>
                                        <option value="天津">天津</option>
                                        <option value="重庆">重庆</option>
                                        <option value="河北">河北</option>
                                        <option value="山西">山西</option>
                                        <option value="辽宁">辽宁</option>
                                        <option value="吉林">吉林</option>
                                        <option value="黑龙江">黑龙江</option>
                                        <option value="江苏">江苏</option>
                                        <option value="浙江">浙江</option>
                                        <option value="安徽">安徽</option>
                                        <option value="福建">福建</option>
                                        <option value="江西">江西</option>
                                        <option value="山东">山东</option>
                                        <option value="河南">河南</option>
                                        <option value="湖北">湖北</option>
                                        <option value="湖南">湖南</option>
                                        <option value="广东">广东</option>
                                        <option value="海南">海南</option>
                                        <option value="四川">四川</option>
                                        <option value="贵州">贵州</option>
                                        <option value="云南">云南</option>
                                        <option value="陕西">陕西</option>
                                        <option value="甘肃">甘肃</option>
                                        <option value="青海">青海</option>
                                        <option value="台湾">台湾</option>
                                        <option value="内蒙古">内蒙古</option>
                                        <option value="广西">广西</option>
                                        <option value="西藏">西藏</option>
                                        <option value="宁夏">宁夏</option>
                                        <option value="新疆">新疆</option>
                                        <option value="香港">香港</option>
                                        <option value="澳门">澳门</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">城市</label>
                                    <input type="text" class="form-control" id="siteCity">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">信息类型 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="siteInfoType" required>
                                        <option value="">请选择信息类型</option>
                                        <option value="招标" selected>招标</option>
                                        <option value="拍卖">拍卖</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">站点状态 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="siteStatus" required>
                                        <option value="">请选择状态</option>
                                        <option value="1" selected>启用</option>
                                        <option value="0">禁用</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">添加人</label>
                                    <input type="text" class="form-control" id="siteAddedBy">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">采集来源ID</label>
                                    <input type="text" class="form-control" id="siteSourceId">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="siteIsTest">
                                <label class="form-check-label" for="siteIsTest">
                                    测试规则
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">URL白名单</label>
                            <textarea class="form-control" id="siteUrlWhitelist" rows="3"
                                      placeholder="每行一个URL模式，用于筛选允许的链接"></textarea>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">URL黑名单</label>
                            <textarea class="form-control" id="siteUrlBlacklist" rows="3"
                                      placeholder="每行一个URL模式，用于过滤不需要的链接"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">采集关键词</label>
                                    <textarea class="form-control" id="siteCollectKeywords" rows="2"
                                              placeholder="用逗号分隔多个关键词"></textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">屏蔽关键词</label>
                                    <textarea class="form-control" id="siteBlockKeywords" rows="2"
                                              placeholder="用逗号分隔多个关键词"></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="app.saveSite()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 采集规则配置模态框 -->
    <div class="modal fade" id="rulesModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">配置采集规则</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="rulesForm">
                        <input type="hidden" id="rulesSiteId">

                        <!-- 测试URL配置 -->
                        <div class="rule-form-section">
                            <h6><i class="bi bi-globe"></i> 测试URL配置</h6>
                            <div class="mb-3">
                                <label class="form-label">测试URL（可选，留空则使用站点默认URL）</label>
                                <input type="url" class="form-control" id="rulesTestUrl"
                                       placeholder="http://example.com/list.html">
                                <div class="form-text">
                                    用于测试规则的URL，建议使用具体的列表页面URL
                                    <br>
                                    <small class="text-muted">
                                        快速测试：
                                        <a href="#" onclick="document.getElementById('rulesTestUrl').value='http://httpbin.org/html'; return false;">httpbin测试页</a> |
                                        <a href="#" onclick="document.getElementById('rulesTestUrl').value='https://www.baidu.com'; return false;">百度首页</a>
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- 链接提取规则 -->
                        <div class="rule-form-section">
                            <h6><i class="bi bi-link"></i> 招标公告链接提取规则</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">提取方式</label>
                                        <select class="form-select" id="rulesLinkType" required>
                                            <option value="">选择提取方式</option>
                                            <option value="re">正则表达式 (re)</option>
                                            <option value="xpath">XPath</option>
                                            <option value="bs4">BeautifulSoup4</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label class="form-label">提取规则</label>
                                        <textarea class="form-control" id="rulesLinkRule" rows="2"
                                                  placeholder="链接提取规则" required></textarea>
                                        <div class="form-text">
                                            <small class="text-muted">
                                                示例：
                                                <a href="#" onclick="document.getElementById('rulesLinkRule').value='//a/@href'; return false;">所有链接</a> |
                                                <a href="#" onclick="document.getElementById('rulesLinkRule').value='//a[contains(@href, \"detail\")]/@href'; return false;">包含detail的链接</a>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-primary"
                                    onclick="app.testRuleStep('link')">
                                <i class="bi bi-play"></i> 测试链接提取
                            </button>
                        </div>

                        <!-- 标题提取规则 -->
                        <div class="rule-form-section">
                            <h6><i class="bi bi-file-text"></i> 招标公告标题提取规则</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">提取方式</label>
                                        <select class="form-select" id="rulesTitleType" required>
                                            <option value="">选择提取方式</option>
                                            <option value="re">正则表达式 (re)</option>
                                            <option value="xpath">XPath</option>
                                            <option value="bs4">BeautifulSoup4</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label class="form-label">提取规则</label>
                                        <textarea class="form-control" id="rulesTitleRule" rows="2"
                                                  placeholder="标题提取规则" required></textarea>
                                        <div class="form-text">
                                            <small class="text-muted">
                                                示例：
                                                <a href="#" onclick="document.getElementById('rulesTitleRule').value='//title/text()'; return false;">页面标题</a> |
                                                <a href="#" onclick="document.getElementById('rulesTitleRule').value='//h1/text()'; return false;">H1标题</a>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 内容提取规则 -->
                        <div class="rule-form-section">
                            <h6><i class="bi bi-file-earmark-text"></i> 招标公告内容提取规则</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">提取方式</label>
                                        <select class="form-select" id="rulesContentType" required>
                                            <option value="">选择提取方式</option>
                                            <option value="re">正则表达式 (re)</option>
                                            <option value="xpath">XPath</option>
                                            <option value="bs4">BeautifulSoup4</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label class="form-label">提取规则</label>
                                        <textarea class="form-control" id="rulesContentRule" rows="2"
                                                  placeholder="内容提取规则" required></textarea>
                                        <div class="form-text">
                                            <small class="text-muted">
                                                示例：
                                                <a href="#" onclick="document.getElementById('rulesContentRule').value='//div[@class=\"content\"]//text()'; return false;">内容区域文本</a> |
                                                <a href="#" onclick="document.getElementById('rulesContentRule').value='//body//text()'; return false;">页面所有文本</a>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">内容测试URL（必填）</label>
                                <input type="url" class="form-control" id="rulesContentTestUrl"
                                       placeholder="http://example.com/detail/123.html">
                                <div class="form-text">
                                    用于测试内容提取的具体详情页URL
                                    <br>
                                    <small class="text-muted">
                                        快速测试：
                                        <a href="#" onclick="document.getElementById('rulesContentTestUrl').value='http://httpbin.org/html'; return false;">httpbin测试页</a> |
                                        <a href="#" onclick="document.getElementById('rulesContentTestUrl').value='https://www.baidu.com'; return false;">百度首页</a>
                                    </small>
                                </div>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-primary"
                                    onclick="app.testRuleStep('content')">
                                <i class="bi bi-play"></i> 测试内容提取
                            </button>
                        </div>

                        <!-- 整体测试 -->
                        <div class="rule-form-section">
                            <h6><i class="bi bi-gear"></i> 整体测试</h6>
                            <button type="button" class="btn btn-success" onclick="app.testRulesFull()">
                                <i class="bi bi-play-circle"></i> 测试完整采集流程
                            </button>
                        </div>

                        <div id="rules-test-result" class="mt-3" style="display: none;">
                            <!-- 规则测试结果 -->
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="app.saveRules()">保存规则</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 结果详情模态框 -->
    <div class="modal fade" id="resultModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">采集结果详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="resultDetail">
                        <!-- 动态加载结果详情 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/main.js"></script>

    <script>
        // 调试信息
        console.log('页面加载完成');
        console.log('CONFIG:', typeof CONFIG !== 'undefined' ? CONFIG : 'CONFIG未定义');
        console.log('api:', typeof api !== 'undefined' ? api : 'api未定义');
        console.log('app:', typeof app !== 'undefined' ? app : 'app未定义');

        // 确保app对象存在后再调用loadDashboard
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成');
            if (typeof app !== 'undefined' && app.loadDashboard) {
                console.log('调用app.loadDashboard()');
                app.loadDashboard();
            } else {
                console.error('app对象或loadDashboard方法不存在');
            }
        });

        // 全局函数：打开API文档
        function openApiDocs() {
            window.open('/docs/api.html', '_blank');
        }

        // 全局函数：打开API信息
        function openApiInfo() {
            window.open('/api/info', '_blank');
        }

        // 全局函数：检查系统健康状态
        function checkHealth() {
            if (typeof app !== 'undefined' && app.checkSystemHealth) {
                app.checkSystemHealth();
            } else {
                // 备用方案
                fetch('/api/health')
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 200) {
                            alert('✅ 系统运行正常\n' + data.message);
                        } else {
                            alert('⚠️ 系统状态异常\n' + data.message);
                        }
                    })
                    .catch(error => {
                        alert('❌ 无法连接到服务器\n' + error.message);
                    });
            }
        }
    </script>
</body>
</html>
