#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
招标采集平台一键启动脚本
"""

import os
import sys
import time
import webbrowser
from pathlib import Path

def main():
    print("=" * 60)
    print("🚀 招标采集平台启动器")
    print("=" * 60)
    
    # 检查依赖
    print("📦 检查依赖...")
    try:
        import flask
        import pymysql
        import requests
        import lxml
        import bs4
        print("✅ 所有依赖已安装")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r backend/requirements.txt")
        return
    
    # 启动后端服务
    print("\n🔧 启动后端服务...")
    
    # 添加backend目录到Python路径
    backend_dir = Path(__file__).parent / "backend"
    sys.path.insert(0, str(backend_dir))
    
    # 设置工作目录
    os.chdir(backend_dir)
    
    try:
        from app import app
        
        print("✅ 后端服务启动成功")
        print("\n" + "=" * 60)
        print("🌐 访问地址:")
        print("   平台首页: http://localhost:5000")
        print("   API信息:  http://localhost:5000/api/info")
        print("   健康检查: http://localhost:5000/api/health")
        print("=" * 60)
        print("💡 提示:")
        print("   - 平台支持响应式设计，在手机、平板、电脑上都能正常使用")
        print("   - 可以在浏览器中直接访问 http://localhost:5000")
        print("   - 按 Ctrl+C 停止服务")
        print("=" * 60)
        
        # 等待2秒后自动打开浏览器
        print("\n⏳ 2秒后自动打开浏览器...")
        time.sleep(2)
        
        try:
            webbrowser.open('http://localhost:5000')
            print("🌐 浏览器已打开")
        except:
            print("⚠️  无法自动打开浏览器，请手动访问 http://localhost:5000")
        
        print("\n🚀 服务运行中...")
        
        # 启动Flask应用
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,  # 生产环境关闭调试模式
            use_reloader=False
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 服务已停止，感谢使用招标采集平台！")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("\n🔧 可能的解决方案:")
        print("1. 检查端口5000是否被占用")
        print("2. 检查Python环境和依赖")
        print("3. 查看错误日志获取更多信息")

if __name__ == "__main__":
    main()
