from flask import Blueprint, request, jsonify
from models.collection_task import CollectionTask
from models.bid_site import BidSite
import requests
import json

collection_tasks_bp = Blueprint('collection_tasks', __name__)

@collection_tasks_bp.route('', methods=['POST'])
def create_collection_task():
    """创建采集任务"""
    try:
        data = request.get_json()
        site_id = data.get('site_id')
        
        if not site_id:
            return jsonify({
                'code': 400,
                'message': '缺少站点ID'
            })
        
        # 检查站点是否存在
        site = BidSite.get_by_id(site_id)
        if not site:
            return jsonify({
                'code': 404,
                'message': '招标站点不存在'
            })
        
        task_id = CollectionTask.create(site_id)
        if task_id:
            # 这里可以调用采集脚本
            # start_collection_task(task_id, site_id)
            
            return jsonify({
                'code': 200,
                'message': '采集任务创建成功',
                'data': {'task_id': task_id}
            })
        else:
            return jsonify({
                'code': 500,
                'message': '创建采集任务失败'
            })
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'创建采集任务失败: {str(e)}'
        })

@collection_tasks_bp.route('/<int:task_id>', methods=['GET'])
def get_collection_task(task_id):
    """获取采集任务详情"""
    try:
        task = CollectionTask.get_by_id(task_id)
        if not task:
            return jsonify({
                'code': 404,
                'message': '采集任务不存在'
            })
        
        return jsonify({
            'code': 200,
            'message': 'success',
            'data': task
        })
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'获取采集任务失败: {str(e)}'
        })

@collection_tasks_bp.route('/site/<int:site_id>', methods=['GET'])
def get_site_collection_tasks(site_id):
    """获取站点的采集任务列表"""
    try:
        limit = int(request.args.get('limit', 10))
        tasks = CollectionTask.get_by_site_id(site_id, limit)
        
        return jsonify({
            'code': 200,
            'message': 'success',
            'data': tasks
        })
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'获取采集任务列表失败: {str(e)}'
        })

@collection_tasks_bp.route('/<int:task_id>/status', methods=['PUT'])
def update_task_status(task_id):
    """更新任务状态"""
    try:
        data = request.get_json()
        status = data.get('status')
        
        if not status:
            return jsonify({
                'code': 400,
                'message': '缺少状态参数'
            })
        
        valid_statuses = ['pending', 'running', 'completed', 'failed']
        if status not in valid_statuses:
            return jsonify({
                'code': 400,
                'message': f'状态必须是 {valid_statuses} 中的一个'
            })
        
        # 提取其他更新参数
        update_params = {}
        for key in ['total_links', 'success_count', 'failed_count', 'error_message']:
            if key in data:
                update_params[key] = data[key]
        
        result = CollectionTask.update_status(task_id, status, **update_params)
        if result:
            return jsonify({
                'code': 200,
                'message': '状态更新成功'
            })
        else:
            return jsonify({
                'code': 500,
                'message': '状态更新失败'
            })
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'更新任务状态失败: {str(e)}'
        })
