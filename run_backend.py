#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的后端启动脚本
"""

import os
import sys
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

# 设置工作目录
os.chdir(backend_dir)

try:
    print("正在启动招标采集平台后端服务...")
    print("=" * 50)
    
    # 导入并启动应用
    from app import app
    
    print("✓ 应用导入成功")
    print("✓ 启动服务在 http://localhost:5000")
    print("✓ 前端页面: ../frontend/index.html")
    print("=" * 50)
    print("按 Ctrl+C 停止服务")
    
    # 启动Flask应用
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=False  # 避免重载问题
    )
    
except Exception as e:
    print(f"✗ 启动失败: {e}")
    print("\n可能的解决方案:")
    print("1. 检查数据库连接配置 (.env文件)")
    print("2. 确保MySQL服务已启动")
    print("3. 运行 python init_db.py 初始化数据库")
    sys.exit(1)
