from .database import db
from datetime import datetime

class BidSite:
    def __init__(self):
        pass
    
    @staticmethod
    def get_all(page=1, page_size=20, filters=None):
        """获取所有招标站点（分页）"""
        offset = (page - 1) * page_size
        
        # 构建查询条件
        where_conditions = ["1=1"]
        params = []

        if filters:
            if filters.get('site_name'):
                where_conditions.append("site_name LIKE %s")
                params.append(f"%{filters['site_name']}%")
            if filters.get('site_url'):
                where_conditions.append("site_url LIKE %s")
                params.append(f"%{filters['site_url']}%")
            if filters.get('province'):
                where_conditions.append("province = %s")
                params.append(filters['province'])
            if filters.get('city'):
                where_conditions.append("city LIKE %s")
                params.append(f"%{filters['city']}%")
            if filters.get('added_by'):
                where_conditions.append("added_by LIKE %s")
                params.append(f"%{filters['added_by']}%")
            if filters.get('info_type'):
                where_conditions.append("info_type = %s")
                params.append(filters['info_type'])
            if filters.get('status') is not None:
                where_conditions.append("status = %s")
                params.append(filters['status'])
            else:
                # 默认只显示启用状态的站点（排除已删除的站点）
                where_conditions.append("status = %s")
                params.append(1)
            if filters.get('is_test') is not None:
                where_conditions.append("is_test = %s")
                params.append(filters['is_test'])
        
        where_clause = " AND ".join(where_conditions)
        
        # 查询总数
        count_sql = f"SELECT COUNT(*) as total FROM bid_sites WHERE {where_clause}"
        count_result = db.execute_query(count_sql, params)
        total = count_result[0]['total'] if count_result else 0
        
        # 查询数据
        sql = f"""
        SELECT id, site_name, site_url, province, city, added_by, is_test,
               source_id, info_type, status, last_run_time, created_at, updated_at
        FROM bid_sites
        WHERE {where_clause}
        ORDER BY created_at DESC
        LIMIT %s OFFSET %s
        """
        params.extend([page_size, offset])
        
        sites = db.execute_query(sql, params)
        
        return {
            'data': sites,
            'total': total,
            'page': page,
            'page_size': page_size,
            'total_pages': (total + page_size - 1) // page_size
        }
    
    @staticmethod
    def get_by_id(site_id):
        """根据ID获取招标站点"""
        sql = "SELECT * FROM bid_sites WHERE id = %s"
        result = db.execute_query(sql, [site_id])
        return result[0] if result else None
    
    @staticmethod
    def create(data):
        """创建招标站点"""
        sql = """
        INSERT INTO bid_sites (site_name, site_url, province, city, url_whitelist,
                              url_blacklist, collect_keywords, block_keywords,
                              added_by, is_test, source_id, info_type, status)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = [
            data.get('site_name'),
            data.get('site_url'),
            data.get('province'),
            data.get('city'),
            data.get('url_whitelist'),
            data.get('url_blacklist'),
            data.get('collect_keywords'),
            data.get('block_keywords'),
            data.get('added_by'),
            data.get('is_test', 0),
            data.get('source_id'),
            data.get('info_type', '招标'),
            data.get('status', 1)
        ]

        result = db.execute_update(sql, params)
        if result:
            return db.get_last_insert_id()
        return None
    
    @staticmethod
    def update(site_id, data):
        """更新招标站点"""
        sql = """
        UPDATE bid_sites
        SET site_name=%s, site_url=%s, province=%s, city=%s, url_whitelist=%s,
            url_blacklist=%s, collect_keywords=%s, block_keywords=%s,
            added_by=%s, is_test=%s, source_id=%s, info_type=%s, status=%s
        WHERE id=%s
        """
        params = [
            data.get('site_name'),
            data.get('site_url'),
            data.get('province'),
            data.get('city'),
            data.get('url_whitelist'),
            data.get('url_blacklist'),
            data.get('collect_keywords'),
            data.get('block_keywords'),
            data.get('added_by'),
            data.get('is_test', 0),
            data.get('source_id'),
            data.get('info_type', '招标'),
            data.get('status', 1),
            site_id
        ]

        return db.execute_update(sql, params)
    
    @staticmethod
    def delete(site_id):
        """删除招标站点（软删除）"""
        # 先检查站点当前状态
        site = BidSite.get_by_id(site_id)
        if not site:
            return 0  # 站点不存在

        if site['status'] == 0:
            return 1  # 站点已经是删除状态，视为成功

        # 更新站点状态为删除状态
        sql = "UPDATE bid_sites SET status = 0 WHERE id = %s"
        return db.execute_update(sql, [site_id])
    
    @staticmethod
    def get_statistics():
        """获取统计信息"""
        # 总站点数
        total_sql = "SELECT COUNT(*) as total FROM bid_sites WHERE status = 1"
        total_result = db.execute_query(total_sql)
        total_sites = total_result[0]['total'] if total_result else 0
        
        # 今日采集统计（这里先返回模拟数据，后续结合采集任务表实现）
        return {
            'total_sites': total_sites,
            'today_collected': 0,  # 今日已采集
            'today_uncollected': total_sites  # 今日未采集
        }

    @staticmethod
    def update_last_run_time(site_id, run_time=None):
        """更新站点最新运行时间"""
        if run_time is None:
            # 如果没有指定时间，使用当前时间
            sql = "UPDATE bid_sites SET last_run_time = NOW() WHERE id = %s"
            params = [site_id]
        else:
            sql = "UPDATE bid_sites SET last_run_time = %s WHERE id = %s"
            params = [run_time, site_id]

        return db.execute_update(sql, params)
