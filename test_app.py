#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的测试应用，不依赖数据库
"""

from flask import Flask, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

@app.route('/', methods=['GET'])
def welcome():
    return jsonify({
        'code': 200,
        'message': '欢迎使用招标采集平台API',
        'data': {
            'name': '招标采集平台',
            'version': '1.0.0',
            'status': 'running',
            'description': '招标信息采集和管理系统'
        }
    })

@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({
        'code': 200,
        'message': 'API服务正常运行',
        'data': {
            'status': 'healthy',
            'version': '1.0.0'
        }
    })

@app.route('/api/statistics', methods=['GET'])
def get_statistics():
    return jsonify({
        'code': 200,
        'message': 'success',
        'data': {
            'total_sites': 3,
            'today_collected': 2,
            'today_uncollected': 1,
            'today_success': 2,
            'today_failed': 0
        }
    })

if __name__ == '__main__':
    print("=" * 50)
    print("招标采集平台测试服务启动")
    print("=" * 50)
    print("访问地址: http://localhost:5000")
    print("健康检查: http://localhost:5000/api/health")
    print("统计数据: http://localhost:5000/api/statistics")
    print("=" * 50)
    print("按 Ctrl+C 停止服务")
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True
    )
