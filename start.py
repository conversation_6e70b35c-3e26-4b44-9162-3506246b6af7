#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
招标采集平台启动脚本
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_requirements():
    """检查依赖是否安装"""
    try:
        import flask
        import pymysql
        import requests
        import lxml
        import bs4
        print("✓ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖: {e}")
        print("请运行: pip install -r backend/requirements.txt")
        return False

def check_database():
    """检查数据库连接"""
    try:
        import pymysql
        from backend.config import Config
        
        connection = pymysql.connect(
            host=Config.MYSQL_HOST,
            port=Config.MYSQL_PORT,
            user=Config.MYSQL_USER,
            password=Config.MYSQL_PASSWORD,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SHOW DATABASES LIKE %s", (Config.MYSQL_DATABASE,))
            result = cursor.fetchone()
            
            if not result:
                print(f"✗ 数据库 {Config.MYSQL_DATABASE} 不存在")
                print("请先创建数据库或运行: python init_db.py")
                return False
            else:
                print("✓ 数据库连接正常")
                return True
                
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        print("请检查数据库配置")
        return False

def start_backend():
    """启动后端服务"""
    print("启动后端服务...")
    backend_dir = Path(__file__).parent / "backend"
    original_dir = os.getcwd()

    try:
        os.chdir(backend_dir)

        # 启动后端服务
        process = subprocess.Popen(
            [sys.executable, "app.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        # 等待服务启动
        import time
        time.sleep(3)

        # 检查服务是否启动成功
        if process.poll() is None:
            print("✓ 后端服务启动成功")
            print("✓ 服务运行在: http://localhost:5000")
            print("\n" + "=" * 50)
            print("服务已启动完成！")
            print("=" * 50)
            print("📱 前端访问: http://localhost:5000")
            print("🔧 API文档: http://localhost:5000/docs/api.html")
            print("💡 系统状态: http://localhost:5000/api/health")
            print("\n按 Ctrl+C 停止服务")
            print("-" * 50)

            # 等待用户中断
            try:
                process.wait()
            except KeyboardInterrupt:
                print("\n正在停止服务...")
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                print("✓ 服务已停止")
        else:
            stdout, stderr = process.communicate()
            print(f"✗ 后端服务启动失败")
            if stderr:
                print(f"错误信息: {stderr}")
            if stdout:
                print(f"输出信息: {stdout}")

    except Exception as e:
        print(f"启动后端服务失败: {e}")
    finally:
        os.chdir(original_dir)

def main():
    print("=" * 50)
    print("招标采集平台启动检查")
    print("=" * 50)
    
    # 检查依赖
    if not check_requirements():
        return
    
    # 检查数据库
    if not check_database():
        return
    
    print("\n" + "=" * 50)
    print("启动服务")
    print("=" * 50)
    
    print("前端访问地址: file://" + str(Path(__file__).parent / "frontend" / "index.html"))
    print("后端API地址: http://localhost:5000")
    print("\n按 Ctrl+C 停止服务")
    print("-" * 50)
    
    # 启动后端
    start_backend()

if __name__ == "__main__":
    main()
