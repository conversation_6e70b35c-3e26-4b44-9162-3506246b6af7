/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80012
 Source Host           : localhost:3306
 Source Schema         : zhaobiao_spider

 Target Server Type    : MySQL
 Target Server Version : 80012
 File Encoding         : 65001

 Date: 28/07/2025 11:56:30
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for bid_sites
-- ----------------------------
DROP TABLE IF EXISTS `bid_sites`;
CREATE TABLE `bid_sites`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `site_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '招标网站名称',
  `site_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '招标网站URL',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '省份',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '城市',
  `url_whitelist` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT 'URL白名单，多个用换行分隔',
  `url_blacklist` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT 'URL黑名单，多个用换行分隔',
  `collect_keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '采集关键词，多个用逗号分隔',
  `block_keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '屏蔽关键词，多个用逗号分隔',
  `added_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '添加人',
  `is_test` tinyint(1) NULL DEFAULT 0 COMMENT '是否是测试规则 0-否 1-是',
  `source_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '采集来源ID',
  `info_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '招标' COMMENT '信息类型：招标、拍卖',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 0-禁用 1-启用',
  `created_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_province_city`(`province`, `city`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_is_test`(`is_test`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '招标站点表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of bid_sites
-- ----------------------------
INSERT INTO `bid_sites` VALUES (1, '中国政府采购网', 'http://www.ccgp.gov.cn/', '全国', '全国', NULL, NULL, NULL, NULL, '系统管理员', 0, 'ccgp_001', '招标', 1, '2025-07-28 10:32:57', '2025-07-28 11:49:45');
INSERT INTO `bid_sites` VALUES (2, '北京市政府采购网', 'http://www.ccgp-beijing.gov.cn/', '北京', '北京', NULL, NULL, NULL, NULL, '系统管理员', 0, 'bj_001', '招标', 1, '2025-07-28 10:32:57', '2025-07-28 11:49:52');
INSERT INTO `bid_sites` VALUES (3, '上海政府采购网', 'http://www.ccgp-shanghai.gov.cn/', '上海', '上海', NULL, NULL, NULL, NULL, '系统管理员', 0, 'sh_001', '招标', 1, '2025-07-28 10:32:57', '2025-07-28 11:49:54');
INSERT INTO `bid_sites` VALUES (7, '6666', 'http://www.baidu.com', '', '', '', '', '', '', '', 0, '', 0, '2025-07-28 11:31:41', '2025-07-28 11:49:37');
INSERT INTO `bid_sites` VALUES (8, '测试站点', 'http://www.baidu.com', '北京', '北京', '', '', '招标,采购', '测试', '测试用户', 1, 'test_001', 0, '2025-07-28 11:41:12', '2025-07-28 11:49:33');

-- ----------------------------
-- Table structure for collection_results
-- ----------------------------
DROP TABLE IF EXISTS `collection_results`;
CREATE TABLE `collection_results`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `task_id` int(11) NOT NULL COMMENT '采集任务ID',
  `site_id` int(11) NOT NULL COMMENT '招标站点ID',
  `site_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '招标网站名称',
  `site_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '招标网站链接',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '省份',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '城市',
  `bid_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '招标公告链接',
  `bid_title` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '招标公告标题',
  `bid_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '招标公告内容',
  `publish_status` enum('unpublished','published') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'unpublished' COMMENT '发布状态',
  `collected_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '采集时间',
  `created_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_task_id`(`task_id`) USING BTREE,
  INDEX `idx_site_id`(`site_id`) USING BTREE,
  INDEX `idx_province_city`(`province`, `city`) USING BTREE,
  INDEX `idx_publish_status`(`publish_status`) USING BTREE,
  INDEX `idx_collected_at`(`collected_at`) USING BTREE,
  FULLTEXT INDEX `ft_title_content`(`bid_title`, `bid_content`),
  CONSTRAINT `collection_results_ibfk_1` FOREIGN KEY (`task_id`) REFERENCES `collection_tasks` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `collection_results_ibfk_2` FOREIGN KEY (`site_id`) REFERENCES `bid_sites` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '采集结果表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of collection_results
-- ----------------------------

-- ----------------------------
-- Table structure for collection_rules
-- ----------------------------
DROP TABLE IF EXISTS `collection_rules`;
CREATE TABLE `collection_rules`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `site_id` int(11) NOT NULL COMMENT '招标站点ID',
  `link_extract_type` enum('re','xpath','bs4') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '链接提取方式',
  `link_extract_rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '招标公告链接提取规则',
  `title_extract_type` enum('re','xpath','bs4') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标题提取方式',
  `title_extract_rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '招标公告标题提取规则',
  `content_extract_type` enum('re','xpath','bs4') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容提取方式',
  `content_extract_rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '招标公告内容提取规则',
  `created_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_site_id`(`site_id`) USING BTREE,
  CONSTRAINT `collection_rules_ibfk_1` FOREIGN KEY (`site_id`) REFERENCES `bid_sites` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '采集规则表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of collection_rules
-- ----------------------------

-- ----------------------------
-- Table structure for collection_tasks
-- ----------------------------
DROP TABLE IF EXISTS `collection_tasks`;
CREATE TABLE `collection_tasks`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `site_id` int(11) NOT NULL COMMENT '招标站点ID',
  `task_status` enum('pending','running','completed','failed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'pending' COMMENT '任务状态',
  `start_time` timestamp(0) NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp(0) NULL DEFAULT NULL COMMENT '结束时间',
  `total_links` int(11) NULL DEFAULT 0 COMMENT '总链接数',
  `success_count` int(11) NULL DEFAULT 0 COMMENT '成功采集数',
  `failed_count` int(11) NULL DEFAULT 0 COMMENT '失败采集数',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `created_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_site_id`(`site_id`) USING BTREE,
  INDEX `idx_task_status`(`task_status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  CONSTRAINT `collection_tasks_ibfk_1` FOREIGN KEY (`site_id`) REFERENCES `bid_sites` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '采集任务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of collection_tasks
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
