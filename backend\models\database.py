import pymysql
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.dirname(current_dir)
project_root = os.path.dirname(backend_dir)
sys.path.insert(0, project_root)

# 尝试不同的导入方式
try:
    from backend.config import Config
except ImportError:
    try:
        from config import Config
    except ImportError:
        # 如果都失败，直接导入配置
        sys.path.insert(0, backend_dir)
        from config import Config

class Database:
    def __init__(self):
        self.connection = None
        
    def connect(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(
                host=Config.MYSQL_HOST,
                port=Config.MYSQL_PORT,
                user=Config.MYSQL_USER,
                password=Config.MYSQL_PASSWORD,
                database=Config.MYSQL_DATABASE,
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor,
                autocommit=True
            )
            return True
        except Exception as e:
            print(f"数据库连接失败: {e}")
            return False
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
    
    def execute_query(self, sql, params=None):
        """执行查询语句"""
        try:
            # 确保连接有效
            if not self.connection or not self._is_connection_alive():
                print("数据库连接无效，尝试重新连接...")
                if not self.connect():
                    print("重新连接失败")
                    return []

            with self.connection.cursor() as cursor:
                cursor.execute(sql, params)
                return cursor.fetchall()
        except Exception as e:
            print(f"查询执行失败: {e}")
            # 尝试重新连接
            try:
                if self.connect():
                    with self.connection.cursor() as cursor:
                        cursor.execute(sql, params)
                        return cursor.fetchall()
            except:
                pass
            return []
    
    def execute_update(self, sql, params=None):
        """执行更新语句"""
        try:
            # 确保连接有效
            if not self.connection or not self._is_connection_alive():
                print("数据库连接无效，尝试重新连接...")
                if not self.connect():
                    print("重新连接失败")
                    return 0

            with self.connection.cursor() as cursor:
                result = cursor.execute(sql, params)
                return result
        except Exception as e:
            print(f"更新执行失败: {e}")
            # 尝试重新连接
            try:
                if self.connect():
                    with self.connection.cursor() as cursor:
                        result = cursor.execute(sql, params)
                        return result
            except:
                pass
            return 0

    def _is_connection_alive(self):
        """检查连接是否有效"""
        try:
            self.connection.ping(reconnect=False)
            return True
        except:
            return False
    
    def get_last_insert_id(self):
        """获取最后插入的ID"""
        return self.connection.insert_id()

# 全局数据库实例
db = Database()
# 自动连接数据库
if not db.connect():
    print("警告: 数据库连接失败")
