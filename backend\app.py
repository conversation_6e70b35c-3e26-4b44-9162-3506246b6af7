from flask import Flask, request, jsonify, send_from_directory, send_file
from flask_cors import CORS
from config import Config
from models.database import db
import os
from pathlib import Path

# 创建Flask应用
app = Flask(__name__)
app.config.from_object(Config)

# 启用CORS
CORS(app)

# 初始化数据库连接
@app.before_request
def init_db():
    if not hasattr(db, 'connection') or not db.connection:
        try:
            print("尝试重新连接数据库...")
            if db.connect():
                print("数据库重新连接成功")
            else:
                print("数据库连接失败，请检查配置")
        except Exception as e:
            print(f"数据库连接异常: {e}")
            # 继续运行，但某些功能可能不可用

# 注册蓝图
from api.bid_sites import bid_sites_bp
from api.collection_rules import collection_rules_bp
from api.collection_tasks import collection_tasks_bp
from api.collection_results import collection_results_bp
from api.spider_test import spider_test_bp

app.register_blueprint(bid_sites_bp, url_prefix='/api/bid_sites')
app.register_blueprint(collection_rules_bp, url_prefix='/api/collection_rules')
app.register_blueprint(collection_tasks_bp, url_prefix='/api/collection_tasks')
app.register_blueprint(collection_results_bp, url_prefix='/api/collection_results')
app.register_blueprint(spider_test_bp, url_prefix='/api/spider_test')

# 首页统计接口
@app.route('/api/statistics', methods=['GET'])
def get_statistics():
    """获取首页统计数据"""
    try:
        from models.bid_site import BidSite
        from models.collection_task import CollectionTask

        # 获取基础统计
        site_stats = BidSite.get_statistics()
        task_stats = CollectionTask.get_today_statistics()

        return jsonify({
            'code': 200,
            'message': 'success',
            'data': {
                'total_sites': site_stats['total_sites'],
                'today_collected': task_stats['today_collected'],
                'today_uncollected': task_stats['today_uncollected'],
                'today_success': task_stats['today_success'],
                'today_failed': task_stats['today_failed']
            }
        })
    except Exception as e:
        # 如果数据库不可用，返回模拟数据
        return jsonify({
            'code': 200,
            'message': 'success (demo data)',
            'data': {
                'total_sites': 3,
                'today_collected': 2,
                'today_uncollected': 1,
                'today_success': 2,
                'today_failed': 0
            }
        })

# 错误处理
@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'code': 404,
        'message': 'API接口不存在'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'code': 500,
        'message': '服务器内部错误'
    }), 500

# 根路径返回前端首页
@app.route('/', methods=['GET'])
def index():
    """返回前端首页"""
    try:
        # 获取前端文件路径
        frontend_dir = Path(__file__).parent.parent / 'frontend'
        index_file = frontend_dir / 'index.html'

        if index_file.exists():
            return send_file(str(index_file))
        else:
            return jsonify({
                'code': 404,
                'message': '前端文件不存在',
                'data': {
                    'expected_path': str(index_file),
                    'api_info': '/api/info'
                }
            }), 404
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'服务器错误: {str(e)}'
        }), 500

# API信息页面
@app.route('/api/info', methods=['GET'])
def api_info():
    """API信息"""
    return jsonify({
        'code': 200,
        'message': '招标采集平台API信息',
        'data': {
            'name': '招标采集平台',
            'version': '1.0.0',
            'description': '招标信息采集和管理系统',
            'endpoints': {
                'health': '/api/health',
                'statistics': '/api/statistics',
                'bid_sites': '/api/bid_sites',
                'collection_rules': '/api/collection_rules',
                'collection_tasks': '/api/collection_tasks',
                'collection_results': '/api/collection_results',
                'spider_test': '/api/spider_test'
            },
            'frontend': '/',
            'docs': '/docs/api.md'
        }
    })

# 静态文件服务
@app.route('/css/<path:filename>')
def serve_css(filename):
    """提供CSS文件"""
    frontend_dir = Path(__file__).parent.parent / 'frontend'
    return send_from_directory(str(frontend_dir / 'css'), filename)

@app.route('/js/<path:filename>')
def serve_js(filename):
    """提供JavaScript文件"""
    frontend_dir = Path(__file__).parent.parent / 'frontend'
    return send_from_directory(str(frontend_dir / 'js'), filename)

@app.route('/pages/<path:filename>')
def serve_pages(filename):
    """提供页面文件"""
    frontend_dir = Path(__file__).parent.parent / 'frontend'
    return send_from_directory(str(frontend_dir / 'pages'), filename)

@app.route('/docs/<path:filename>')
def serve_docs(filename):
    """提供文档文件"""
    docs_dir = Path(__file__).parent.parent / 'docs'
    return send_from_directory(str(docs_dir), filename)

@app.route('/<path:filename>')
def serve_frontend_files(filename):
    """提供前端文件"""
    frontend_dir = Path(__file__).parent.parent / 'frontend'
    return send_from_directory(str(frontend_dir), filename)

# 健康检查接口
@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({
        'code': 200,
        'message': 'API服务正常运行',
        'data': {
            'status': 'healthy',
            'version': '1.0.0'
        }
    })

if __name__ == '__main__':
    # app.run(
    #     host='0.0.0.0',
    #     port=5010,
    #     debug=Config.DEBUG
    # )
    app.run(debug=True, host='0.0.0.0', port=5009, use_reloader=False)
