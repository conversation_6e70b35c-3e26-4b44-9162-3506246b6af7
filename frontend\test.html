<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .test-card {
            margin: 20px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>统计卡片测试</h1>
        
        <div class="row">
            <div class="col-md-3">
                <div class="card text-white bg-primary mb-3 test-card">
                    <div class="card-header">总招标站点数</div>
                    <div class="card-body">
                        <h4 class="card-title" id="total-sites">8</h4>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-success mb-3 test-card">
                    <div class="card-header">今日已采集</div>
                    <div class="card-body">
                        <h4 class="card-title" id="today-collected">5</h4>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-warning mb-3 test-card">
                    <div class="card-header">今日未采集</div>
                    <div class="card-body">
                        <h4 class="card-title" id="today-uncollected">3</h4>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-info mb-3 test-card">
                    <div class="card-header">今日成功率</div>
                    <div class="card-body">
                        <h4 class="card-title" id="success-rate">80%</h4>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <button class="btn btn-primary" onclick="loadStats()">加载统计数据</button>
            <button class="btn btn-secondary" onclick="testAPI()">测试API</button>
        </div>
        
        <div id="debug-info" class="mt-4">
            <h3>调试信息</h3>
            <pre id="debug-output"></pre>
        </div>
    </div>

    <script>
        async function loadStats() {
            try {
                const response = await fetch('http://localhost:5000/api/statistics');
                const result = await response.json();
                
                document.getElementById('debug-output').textContent = JSON.stringify(result, null, 2);
                
                if (result.code === 200) {
                    const data = result.data;
                    document.getElementById('total-sites').textContent = data.total_sites || 0;
                    document.getElementById('today-collected').textContent = data.today_collected || 0;
                    document.getElementById('today-uncollected').textContent = data.today_uncollected || 0;
                    
                    const collected = data.today_collected || 0;
                    const failed = data.today_failed || 0;
                    const total = collected + failed;
                    const successRate = total > 0 ? Math.round((collected / total) * 100) : 0;
                    document.getElementById('success-rate').textContent = `${successRate}%`;
                }
            } catch (error) {
                document.getElementById('debug-output').textContent = 'Error: ' + error.message;
            }
        }
        
        async function testAPI() {
            try {
                const response = await fetch('http://localhost:5000/api/health');
                const result = await response.json();
                document.getElementById('debug-output').textContent = 'Health check: ' + JSON.stringify(result, null, 2);
            } catch (error) {
                document.getElementById('debug-output').textContent = 'API Error: ' + error.message;
            }
        }
        
        // 页面加载时自动加载统计数据
        window.onload = function() {
            loadStats();
        };
    </script>
</body>
</html>
