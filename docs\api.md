# API接口文档

## 基础信息
- 基础URL: `http://localhost:5000/api`
- 数据格式: JSON
- 字符编码: UTF-8

## 通用响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {}
}
```

## 1. 系统接口

### 1.1 健康检查
- **URL**: `/health`
- **方法**: GET
- **说明**: 检查API服务状态

**响应示例**:
```json
{
    "code": 200,
    "message": "API服务正常运行",
    "data": {
        "status": "healthy",
        "version": "1.0.0"
    }
}
```

### 1.2 获取统计数据
- **URL**: `/statistics`
- **方法**: GET
- **说明**: 获取首页统计信息

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "total_sites": 10,
        "today_collected": 5,
        "today_uncollected": 5,
        "today_success": 4,
        "today_failed": 1
    }
}
```

## 2. 招标站点接口

### 2.1 获取站点列表
- **URL**: `/bid_sites`
- **方法**: GET
- **说明**: 获取招标站点列表（分页）

**查询参数**:
- `page`: 页码（默认1）
- `page_size`: 每页数量（默认20）
- `site_name`: 站点名称筛选
- `province`: 省份筛选
- `city`: 城市筛选
- `is_test`: 是否测试规则（0/1）

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "data": [
            {
                "id": 1,
                "site_name": "中国政府采购网",
                "site_url": "http://www.ccgp.gov.cn/",
                "province": "全国",
                "city": "全国",
                "added_by": "系统管理员",
                "is_test": 0,
                "created_at": "2024-01-01 10:00:00"
            }
        ],
        "total": 1,
        "page": 1,
        "page_size": 20,
        "total_pages": 1
    }
}
```

### 2.2 获取单个站点
- **URL**: `/bid_sites/{id}`
- **方法**: GET
- **说明**: 获取指定站点详情

### 2.3 创建站点
- **URL**: `/bid_sites`
- **方法**: POST
- **说明**: 创建新的招标站点

**请求体**:
```json
{
    "site_name": "站点名称",
    "site_url": "http://example.com",
    "province": "北京",
    "city": "北京",
    "added_by": "管理员",
    "is_test": 0,
    "url_whitelist": "白名单URL模式",
    "url_blacklist": "黑名单URL模式",
    "collect_keywords": "关键词1,关键词2",
    "block_keywords": "屏蔽词1,屏蔽词2",
    "source_id": "来源ID"
}
```

### 2.4 更新站点
- **URL**: `/bid_sites/{id}`
- **方法**: PUT
- **说明**: 更新指定站点信息

### 2.5 删除站点
- **URL**: `/bid_sites/{id}`
- **方法**: DELETE
- **说明**: 删除指定站点（软删除）

## 3. 采集规则接口

### 3.1 获取采集规则
- **URL**: `/collection_rules/{site_id}`
- **方法**: GET
- **说明**: 获取指定站点的采集规则

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 1,
        "site_id": 1,
        "link_extract_type": "re",
        "link_extract_rule": "href=\"([^\"]*\\.html?)\"",
        "title_extract_type": "xpath",
        "title_extract_rule": "//title/text()",
        "content_extract_type": "bs4",
        "content_extract_rule": "div.content"
    }
}
```

### 3.2 保存采集规则
- **URL**: `/collection_rules/{site_id}`
- **方法**: POST/PUT
- **说明**: 创建或更新采集规则

**请求体**:
```json
{
    "link_extract_type": "re",
    "link_extract_rule": "href=\"([^\"]*\\.html?)\"",
    "title_extract_type": "xpath",
    "title_extract_rule": "//title/text()",
    "content_extract_type": "bs4",
    "content_extract_rule": "div.content"
}
```

## 4. 采集任务接口

### 4.1 创建采集任务
- **URL**: `/collection_tasks`
- **方法**: POST
- **说明**: 创建新的采集任务

**请求体**:
```json
{
    "site_id": 1
}
```

### 4.2 获取任务详情
- **URL**: `/collection_tasks/{id}`
- **方法**: GET
- **说明**: 获取指定任务详情

### 4.3 更新任务状态
- **URL**: `/collection_tasks/{id}/status`
- **方法**: PUT
- **说明**: 更新任务状态

**请求体**:
```json
{
    "status": "completed",
    "total_links": 10,
    "success_count": 8,
    "failed_count": 2
}
```

## 5. 采集结果接口

### 5.1 获取采集结果
- **URL**: `/collection_results`
- **方法**: GET
- **说明**: 获取采集结果列表（分页）

**查询参数**:
- `page`: 页码
- `page_size`: 每页数量
- `site_name`: 站点名称筛选
- `province`: 省份筛选
- `publish_status`: 发布状态筛选
- `start_date`: 开始日期
- `end_date`: 结束日期
- `keyword`: 关键词搜索

### 5.2 获取结果详情
- **URL**: `/collection_results/{id}`
- **方法**: GET
- **说明**: 获取指定结果详情

### 5.3 更新发布状态
- **URL**: `/collection_results/{id}/publish`
- **方法**: PUT
- **说明**: 更新结果发布状态

**请求体**:
```json
{
    "publish_status": "published"
}
```

## 6. 测试接口

### 6.1 测试链接提取
- **URL**: `/spider_test/links`
- **方法**: POST
- **说明**: 测试链接提取规则

**请求体**:
```json
{
    "url": "http://example.com",
    "extract_type": "re",
    "extract_rule": "href=\"([^\"]*)\""
}
```

### 6.2 测试内容提取
- **URL**: `/spider_test/content`
- **方法**: POST
- **说明**: 测试内容提取规则

**请求体**:
```json
{
    "url": "http://example.com/detail.html",
    "title_extract_type": "xpath",
    "title_extract_rule": "//title/text()",
    "content_extract_type": "bs4",
    "content_extract_rule": "div.content"
}
```

### 6.3 测试完整流程
- **URL**: `/spider_test/full`
- **方法**: POST
- **说明**: 测试完整采集流程

**请求体**:
```json
{
    "list_url": "http://example.com/list.html",
    "link_extract_type": "re",
    "link_extract_rule": "href=\"([^\"]*\\.html?)\"",
    "title_extract_type": "xpath",
    "title_extract_rule": "//title/text()",
    "content_extract_type": "bs4",
    "content_extract_rule": "div.content",
    "max_links": 5
}
```

## 错误码说明

- `200`: 成功
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

## 提取方式说明

### 1. 正则表达式 (re)
使用Python re模块进行模式匹配，支持分组提取。

### 2. XPath
使用lxml库进行XPath选择，适合结构化HTML文档。

### 3. BeautifulSoup4 (bs4)
支持CSS选择器和标签选择器：
- CSS选择器：以 `css:` 开头，如 `css:div.content`
- 标签选择器：直接使用标签名和类名，如 `div.content`
