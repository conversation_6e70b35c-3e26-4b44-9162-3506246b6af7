#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最简单的HTTP服务器测试
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json

class RequestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            response = {
                'code': 200,
                'message': '欢迎使用招标采集平台API',
                'data': {
                    'name': '招标采集平台',
                    'version': '1.0.0',
                    'status': 'running'
                }
            }
        elif self.path == '/api/health':
            response = {
                'code': 200,
                'message': 'API服务正常运行',
                'data': {
                    'status': 'healthy',
                    'version': '1.0.0'
                }
            }
        else:
            response = {
                'code': 404,
                'message': 'API接口不存在'
            }
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        response_json = json.dumps(response, ensure_ascii=False)
        self.wfile.write(response_json.encode('utf-8'))
    
    def log_message(self, format, *args):
        print(f"[{self.address_string()}] {format % args}")

if __name__ == '__main__':
    server_address = ('', 5000)
    httpd = HTTPServer(server_address, RequestHandler)
    
    print("=" * 50)
    print("招标采集平台简单服务器启动")
    print("=" * 50)
    print("访问地址: http://localhost:5000")
    print("健康检查: http://localhost:5000/api/health")
    print("=" * 50)
    print("按 Ctrl+C 停止服务")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n服务器已停止")
        httpd.server_close()
