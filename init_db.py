#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
"""

import pymysql
import os
from pathlib import Path

def init_database():
    """初始化数据库"""
    
    # 数据库配置
    config = {
        'host': '*************',
        'port': 3306,
        'user': 'root',
        'password': 'ZH#@!wl4007079888',  # 请根据实际情况修改
        'charset': 'utf8mb4'
    }
    
    print("连接MySQL服务器...")
    try:
        connection = pymysql.connect(**config)
        print("✓ MySQL连接成功")
        
        # 读取SQL文件
        sql_file = Path(__file__).parent / "database" / "init.sql"
        if not sql_file.exists():
            print(f"✗ SQL文件不存在: {sql_file}")
            return False
        
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 分割SQL语句
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        with connection.cursor() as cursor:
            for i, statement in enumerate(sql_statements):
                try:
                    cursor.execute(statement)
                    print(f"✓ 执行SQL语句 {i+1}/{len(sql_statements)}")
                except Exception as e:
                    print(f"✗ 执行SQL语句失败: {e}")
                    print(f"语句: {statement[:100]}...")
        
        connection.commit()
        print("✓ 数据库初始化完成")
        return True
        
    except Exception as e:
        print(f"✗ 数据库初始化失败: {e}")
        return False
    finally:
        if 'connection' in locals():
            connection.close()

def main():
    print("=" * 50)
    print("招标采集平台 - 数据库初始化")
    print("=" * 50)
    
    print("注意: 请确保MySQL服务已启动，并根据需要修改数据库配置")
    print("配置文件: .env")
    print()
    
    confirm = input("是否继续初始化数据库? (y/N): ")
    if confirm.lower() != 'y':
        print("取消初始化")
        return
    
    if init_database():
        print("\n数据库初始化成功！")
        print("现在可以运行: python start.py 启动服务")
    else:
        print("\n数据库初始化失败！")
        print("请检查MySQL配置和权限")

if __name__ == "__main__":
    main()
