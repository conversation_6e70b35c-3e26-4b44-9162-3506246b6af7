#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API测试脚本
"""

import requests
import json

def test_create_site():
    """测试创建招标站点"""
    url = "http://localhost:5000/api/bid_sites"
    
    data = {
        "site_name": "测试站点",
        "site_url": "http://www.baidu.com",
        "province": "北京",
        "city": "北京",
        "added_by": "测试用户",
        "source_id": "test_001",
        "is_test": 1,
        "url_whitelist": "",
        "url_blacklist": "",
        "collect_keywords": "",
        "block_keywords": ""
    }
    
    try:
        response = requests.post(url, json=data, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"创建成功，站点ID: {result.get('data', {}).get('id')}")
        else:
            print("创建失败")
            
    except Exception as e:
        print(f"请求失败: {e}")

def test_get_sites():
    """测试获取站点列表"""
    url = "http://localhost:5000/api/bid_sites"
    
    try:
        response = requests.get(url, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    print("=" * 50)
    print("测试创建招标站点")
    print("=" * 50)
    test_create_site()
    
    print("\n" + "=" * 50)
    print("测试获取站点列表")
    print("=" * 50)
    test_get_sites()
