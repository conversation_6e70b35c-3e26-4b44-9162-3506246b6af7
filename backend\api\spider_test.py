from flask import Blueprint, request, jsonify
import requests
import sys
import os

# 添加spider目录到路径
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), '..', 'spider'))

spider_test_bp = Blueprint('spider_test', __name__)

@spider_test_bp.route('/links', methods=['POST'])
def test_link_extraction():
    """测试链接提取规则"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['url', 'extract_type', 'extract_rule']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'code': 400,
                    'message': f'缺少必填字段: {field}'
                })
        
        url = data.get('url')
        extract_type = data.get('extract_type')
        extract_rule = data.get('extract_rule')
        
        # 验证提取方式
        if extract_type not in ['re', 'xpath', 'bs4']:
            return jsonify({
                'code': 400,
                'message': '提取方式必须是 re、xpath 或 bs4'
            })
        
        # 调用采集脚本进行测试
        try:
            from spider import test_link_extraction as spider_test_links
            result = spider_test_links(url, extract_type, extract_rule)
            
            return jsonify({
                'code': 200,
                'message': 'success',
                'data': {
                    'url': url,
                    'extract_type': extract_type,
                    'extract_rule': extract_rule,
                    'links': result.get('links', []),
                    'total_count': len(result.get('links', [])),
                    'html': result.get('html'),  # 添加HTML源码
                    'error': result.get('error')
                }
            })
        except Exception as e:
            return jsonify({
                'code': 500,
                'message': f'链接提取测试失败: {str(e)}'
            })
            
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'测试请求处理失败: {str(e)}'
        })

@spider_test_bp.route('/content', methods=['POST'])
def test_content_extraction():
    """测试内容提取规则"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['url', 'title_extract_type', 'title_extract_rule', 
                          'content_extract_type', 'content_extract_rule']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'code': 400,
                    'message': f'缺少必填字段: {field}'
                })
        
        url = data.get('url')
        title_extract_type = data.get('title_extract_type')
        title_extract_rule = data.get('title_extract_rule')
        content_extract_type = data.get('content_extract_type')
        content_extract_rule = data.get('content_extract_rule')
        
        # 验证提取方式
        valid_types = ['re', 'xpath', 'bs4']
        if title_extract_type not in valid_types or content_extract_type not in valid_types:
            return jsonify({
                'code': 400,
                'message': '提取方式必须是 re、xpath 或 bs4'
            })
        
        # 调用采集脚本进行测试
        try:
            from spider import test_content_extraction as spider_test_content
            result = spider_test_content(
                url, 
                title_extract_type, title_extract_rule,
                content_extract_type, content_extract_rule
            )
            
            return jsonify({
                'code': 200,
                'message': 'success',
                'data': {
                    'url': url,
                    'title': result.get('title'),
                    'content': result.get('content'),
                    'html': result.get('html'),  # 添加HTML源码
                    'error': result.get('error')
                }
            })
        except Exception as e:
            return jsonify({
                'code': 500,
                'message': f'内容提取测试失败: {str(e)}'
            })
            
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'测试请求处理失败: {str(e)}'
        })

@spider_test_bp.route('/full', methods=['POST'])
def test_full_extraction():
    """测试完整采集流程"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = [
            'list_url', 'link_extract_type', 'link_extract_rule',
            'title_extract_type', 'title_extract_rule',
            'content_extract_type', 'content_extract_rule'
        ]
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'code': 400,
                    'message': f'缺少必填字段: {field}'
                })
        
        list_url = data.get('list_url')
        link_extract_type = data.get('link_extract_type')
        link_extract_rule = data.get('link_extract_rule')
        title_extract_type = data.get('title_extract_type')
        title_extract_rule = data.get('title_extract_rule')
        content_extract_type = data.get('content_extract_type')
        content_extract_rule = data.get('content_extract_rule')
        max_links = int(data.get('max_links', 5))  # 限制测试链接数量
        
        # 验证提取方式
        valid_types = ['re', 'xpath', 'bs4']
        extract_types = [link_extract_type, title_extract_type, content_extract_type]
        if not all(t in valid_types for t in extract_types):
            return jsonify({
                'code': 400,
                'message': '提取方式必须是 re、xpath 或 bs4'
            })
        
        # 调用采集脚本进行测试
        try:
            from spider import test_full_extraction as spider_test_full
            result = spider_test_full(
                list_url,
                link_extract_type, link_extract_rule,
                title_extract_type, title_extract_rule,
                content_extract_type, content_extract_rule,
                max_links
            )
            
            return jsonify({
                'code': 200,
                'message': 'success',
                'data': result
            })
        except Exception as e:
            return jsonify({
                'code': 500,
                'message': f'完整采集测试失败: {str(e)}'
            })
            
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'测试请求处理失败: {str(e)}'
        })
