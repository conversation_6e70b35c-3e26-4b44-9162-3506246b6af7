#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统功能测试脚本
"""

import requests
import json
import time
import sys
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent / "backend"))
sys.path.append(str(Path(__file__).parent / "spider"))

API_BASE = "http://localhost:5000/api"

def test_api_health():
    """测试API健康检查"""
    try:
        response = requests.get(f"{API_BASE}/health", timeout=5)
        if response.status_code == 200:
            print("✓ API健康检查通过")
            return True
        else:
            print(f"✗ API健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ API连接失败: {e}")
        return False

def test_statistics():
    """测试统计接口"""
    try:
        response = requests.get(f"{API_BASE}/statistics", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✓ 统计接口正常")
            print(f"  总站点数: {data.get('data', {}).get('total_sites', 0)}")
            return True
        else:
            print(f"✗ 统计接口失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 统计接口错误: {e}")
        return False

def test_bid_sites():
    """测试招标站点接口"""
    try:
        # 获取站点列表
        response = requests.get(f"{API_BASE}/bid_sites", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✓ 招标站点列表接口正常")
            sites = data.get('data', {}).get('data', [])
            print(f"  当前站点数: {len(sites)}")
            return True
        else:
            print(f"✗ 招标站点接口失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 招标站点接口错误: {e}")
        return False

def test_spider_functions():
    """测试采集功能"""
    try:
        from spider import test_link_extraction, test_content_extraction
        
        # 测试链接提取
        print("测试链接提取功能...")
        test_url = "https://httpbin.org/html"
        test_pattern = r'href="([^"]*)"'
        
        result = test_link_extraction(test_url, 're', test_pattern)
        if result.get('links'):
            print("✓ 链接提取功能正常")
        else:
            print("✗ 链接提取功能异常")
            print(f"  错误: {result.get('error')}")
        
        # 测试内容提取
        print("测试内容提取功能...")
        result = test_content_extraction(
            test_url, 
            're', r'<title>(.*?)</title>',
            're', r'<body>(.*?)</body>'
        )
        
        if result.get('title') or result.get('content'):
            print("✓ 内容提取功能正常")
        else:
            print("✗ 内容提取功能异常")
            print(f"  错误: {result.get('error')}")
        
        return True
        
    except Exception as e:
        print(f"✗ 采集功能测试失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    try:
        from backend.models.database import db
        
        if db.connect():
            print("✓ 数据库连接正常")
            
            # 测试查询
            result = db.execute_query("SELECT COUNT(*) as count FROM bid_sites")
            if result:
                print(f"  站点表记录数: {result[0]['count']}")
            
            db.close()
            return True
        else:
            print("✗ 数据库连接失败")
            return False
            
    except Exception as e:
        print(f"✗ 数据库测试失败: {e}")
        return False

def main():
    print("=" * 50)
    print("招标采集平台 - 系统功能测试")
    print("=" * 50)
    
    tests = [
        ("数据库连接", test_database_connection),
        ("API健康检查", test_api_health),
        ("统计接口", test_statistics),
        ("招标站点接口", test_bid_sites),
        ("采集功能", test_spider_functions),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
            else:
                print(f"测试失败: {test_name}")
        except Exception as e:
            print(f"测试异常: {test_name} - {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过，系统运行正常！")
    else:
        print("✗ 部分测试失败，请检查相关配置")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
