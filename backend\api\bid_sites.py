from flask import Blueprint, request, jsonify
from models.bid_site import BidSite
from models.collection_rule import CollectionRule

bid_sites_bp = Blueprint('bid_sites', __name__)

@bid_sites_bp.route('', methods=['GET'])
def get_bid_sites():
    """获取招标站点列表"""
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        
        # 获取筛选条件
        filters = {}
        if request.args.get('site_name'):
            filters['site_name'] = request.args.get('site_name')
        if request.args.get('site_url'):
            filters['site_url'] = request.args.get('site_url')
        if request.args.get('province'):
            filters['province'] = request.args.get('province')
        if request.args.get('city'):
            filters['city'] = request.args.get('city')
        if request.args.get('added_by'):
            filters['added_by'] = request.args.get('added_by')
        if request.args.get('info_type'):
            filters['info_type'] = request.args.get('info_type')
        if request.args.get('status') is not None:
            filters['status'] = int(request.args.get('status'))
        if request.args.get('is_test') is not None:
            filters['is_test'] = int(request.args.get('is_test'))
        
        result = BidSite.get_all(page, page_size, filters)
        
        return jsonify({
            'code': 200,
            'message': 'success',
            'data': result
        })
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'获取招标站点列表失败: {str(e)}'
        })

@bid_sites_bp.route('/<int:site_id>', methods=['GET'])
def get_bid_site(site_id):
    """获取单个招标站点详情"""
    try:
        site = BidSite.get_by_id(site_id)
        if not site:
            return jsonify({
                'code': 404,
                'message': '招标站点不存在'
            })
        
        # 获取采集规则
        rules = CollectionRule.get_by_site_id(site_id)
        site['rules'] = rules
        
        return jsonify({
            'code': 200,
            'message': 'success',
            'data': site
        })
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'获取招标站点详情失败: {str(e)}'
        })

@bid_sites_bp.route('', methods=['POST'])
def create_bid_site():
    """创建招标站点"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['site_name', 'site_url']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'code': 400,
                    'message': f'缺少必填字段: {field}'
                })
        
        site_id = BidSite.create(data)
        if site_id:
            return jsonify({
                'code': 200,
                'message': '创建成功',
                'data': {'id': site_id}
            })
        else:
            return jsonify({
                'code': 500,
                'message': '创建失败，请检查数据库连接'
            })
    except Exception as e:
        print(f"创建招标站点错误: {e}")  # 在控制台输出详细错误
        return jsonify({
            'code': 500,
            'message': f'创建招标站点失败: {str(e)}'
        })

@bid_sites_bp.route('/<int:site_id>', methods=['PUT'])
def update_bid_site(site_id):
    """更新招标站点"""
    try:
        data = request.get_json()
        
        # 检查站点是否存在
        site = BidSite.get_by_id(site_id)
        if not site:
            return jsonify({
                'code': 404,
                'message': '招标站点不存在'
            })
        
        result = BidSite.update(site_id, data)
        if result:
            return jsonify({
                'code': 200,
                'message': '更新成功'
            })
        else:
            return jsonify({
                'code': 500,
                'message': '更新失败'
            })
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'更新招标站点失败: {str(e)}'
        })

@bid_sites_bp.route('/<int:site_id>', methods=['DELETE'])
def delete_bid_site(site_id):
    """删除招标站点"""
    try:
        # 检查站点是否存在
        site = BidSite.get_by_id(site_id)
        if not site:
            return jsonify({
                'code': 404,
                'message': '招标站点不存在'
            })
        
        result = BidSite.delete(site_id)
        print(f"删除结果: {result}")  # 添加调试信息
        if result and result > 0:
            return jsonify({
                'code': 200,
                'message': '删除成功'
            })
        else:
            return jsonify({
                'code': 500,
                'message': f'删除失败，影响行数: {result}'
            })
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'删除招标站点失败: {str(e)}'
        })

@bid_sites_bp.route('/<int:site_id>/run', methods=['POST'])
def update_site_run_time(site_id):
    """更新站点最新运行时间"""
    try:
        # 检查站点是否存在
        site = BidSite.get_by_id(site_id)
        if not site:
            return jsonify({
                'code': 404,
                'message': '招标站点不存在'
            })

        data = request.get_json() or {}
        run_time = data.get('run_time')  # 可选，如果不提供则使用当前时间

        result = BidSite.update_last_run_time(site_id, run_time)
        if result:
            return jsonify({
                'code': 200,
                'message': '运行时间更新成功'
            })
        else:
            return jsonify({
                'code': 500,
                'message': '更新运行时间失败'
            })
    except Exception as e:
        print(f"更新运行时间错误: {e}")  # 在控制台输出详细错误
        return jsonify({
            'code': 500,
            'message': f'更新运行时间失败: {str(e)}'
        })
