#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
采集工具函数
"""

import re
import time
import hashlib
from urllib.parse import urlparse, urljoin
from typing import List, Dict, Optional

def is_valid_url(url: str) -> bool:
    """检查URL是否有效"""
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except:
        return False

def normalize_url(url: str, base_url: str = None) -> str:
    """标准化URL"""
    if not url:
        return ''
    
    url = url.strip()
    
    # 如果是相对URL，转换为绝对URL
    if base_url and not url.startswith(('http://', 'https://')):
        url = urljoin(base_url, url)
    
    return url

def clean_text(text: str) -> str:
    """清理文本"""
    if not text:
        return ''
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text)
    
    # 移除首尾空白
    text = text.strip()
    
    return text

def extract_domain(url: str) -> str:
    """提取域名"""
    try:
        parsed = urlparse(url)
        return parsed.netloc
    except:
        return ''

def is_url_allowed(url: str, whitelist: List[str] = None, blacklist: List[str] = None) -> bool:
    """检查URL是否被允许"""
    if not url:
        return False
    
    # 检查黑名单
    if blacklist:
        for pattern in blacklist:
            if pattern and pattern in url:
                return False
    
    # 检查白名单
    if whitelist:
        for pattern in whitelist:
            if pattern and pattern in url:
                return True
        return False  # 有白名单但不匹配
    
    return True

def contains_keywords(text: str, keywords: List[str]) -> bool:
    """检查文本是否包含关键词"""
    if not text or not keywords:
        return False
    
    text_lower = text.lower()
    for keyword in keywords:
        if keyword and keyword.lower() in text_lower:
            return True
    
    return False

def should_block_content(title: str, content: str, block_keywords: List[str]) -> bool:
    """检查内容是否应该被屏蔽"""
    if not block_keywords:
        return False
    
    combined_text = f"{title} {content}".lower()
    
    for keyword in block_keywords:
        if keyword and keyword.lower() in combined_text:
            return True
    
    return False

def generate_content_hash(title: str, content: str) -> str:
    """生成内容哈希值，用于去重"""
    combined = f"{title.strip()}{content.strip()}"
    return hashlib.md5(combined.encode('utf-8')).hexdigest()

def parse_keywords(keywords_str: str) -> List[str]:
    """解析关键词字符串"""
    if not keywords_str:
        return []
    
    # 支持逗号、分号、换行分隔
    keywords = re.split(r'[,;，；\n\r]+', keywords_str)
    
    # 清理并过滤空值
    return [kw.strip() for kw in keywords if kw.strip()]

def parse_url_list(urls_str: str) -> List[str]:
    """解析URL列表字符串"""
    if not urls_str:
        return []
    
    # 支持换行分隔
    urls = urls_str.strip().split('\n')
    
    # 清理并过滤无效URL
    return [url.strip() for url in urls if url.strip()]

def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"

def get_retry_delay(attempt: int, base_delay: float = 1.0, max_delay: float = 60.0) -> float:
    """计算重试延迟时间（指数退避）"""
    delay = base_delay * (2 ** attempt)
    return min(delay, max_delay)

def validate_regex(pattern: str) -> bool:
    """验证正则表达式是否有效"""
    try:
        re.compile(pattern)
        return True
    except re.error:
        return False

def validate_xpath(xpath: str) -> bool:
    """验证XPath表达式是否有效（简单检查）"""
    if not xpath:
        return False
    
    # 基本的XPath语法检查
    invalid_chars = ['<', '>', '"', "'"]
    for char in invalid_chars:
        if char in xpath and not (xpath.count('"') % 2 == 0 and xpath.count("'") % 2 == 0):
            return False
    
    return True

def validate_bs4_selector(selector: str) -> bool:
    """验证BeautifulSoup4选择器是否有效（简单检查）"""
    if not selector:
        return False
    
    # 基本检查
    if selector.startswith('css:'):
        # CSS选择器
        return len(selector) > 4
    else:
        # 标签选择器
        return True

class RateLimiter:
    """速率限制器"""
    
    def __init__(self, max_requests: int = 10, time_window: int = 60):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = []
    
    def can_request(self) -> bool:
        """检查是否可以发起请求"""
        now = time.time()
        
        # 清理过期的请求记录
        self.requests = [req_time for req_time in self.requests if now - req_time < self.time_window]
        
        return len(self.requests) < self.max_requests
    
    def record_request(self):
        """记录请求"""
        self.requests.append(time.time())
    
    def wait_time(self) -> float:
        """计算需要等待的时间"""
        if self.can_request():
            return 0
        
        now = time.time()
        oldest_request = min(self.requests)
        return self.time_window - (now - oldest_request)

def create_extraction_rules_example():
    """创建提取规则示例"""
    return {
        'regex_examples': {
            'links': r'href="([^"]*\.html?[^"]*)"',
            'title': r'<title[^>]*>([^<]+)</title>',
            'content': r'<div[^>]*class="content"[^>]*>(.*?)</div>'
        },
        'xpath_examples': {
            'links': '//a/@href',
            'title': '//title/text()',
            'content': '//div[@class="content"]//text()'
        },
        'bs4_examples': {
            'links': 'css:a[href]',
            'title': 'title',
            'content': 'div.content'
        }
    }

if __name__ == '__main__':
    # 测试工具函数
    print("工具函数测试")
    
    # 测试URL验证
    test_urls = [
        'http://example.com',
        'https://example.com/path',
        'invalid-url',
        'ftp://example.com'
    ]
    
    for url in test_urls:
        print(f"URL: {url}, 有效: {is_valid_url(url)}")
    
    # 测试关键词解析
    keywords_str = "招标,采购；投标，竞标\n公告"
    keywords = parse_keywords(keywords_str)
    print(f"关键词: {keywords}")
    
    # 测试正则表达式验证
    regex_patterns = [
        r'href="([^"]*)"',
        r'[invalid regex',
        r'<title>(.*?)</title>'
    ]
    
    for pattern in regex_patterns:
        print(f"正则: {pattern}, 有效: {validate_regex(pattern)}")
