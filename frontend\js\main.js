// 主要JavaScript逻辑
class App {
    constructor() {
        this.currentPage = 'dashboard';
        this.currentSitePage = 1;
        this.currentResultPage = 1;
        this.init();
    }

    // 初始化应用
    init() {
        this.loadDashboard();
        this.bindEvents();
    }

    // 绑定事件
    bindEvents() {
        // 绑定测试表单事件
        this.bindTestEvents();

        // 绑定站点表单事件
        this.bindSiteFormEvents();

        // 绑定筛选条件事件
        this.bindFilterEvents();
    }

    // 绑定测试相关事件
    bindTestEvents() {
        // 链接提取测试
        const linkTestForm = document.getElementById('link-test-form');
        if (linkTestForm) {
            linkTestForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.testLinkExtraction();
            });
        }

        // 内容提取测试
        const contentTestForm = document.getElementById('content-test-form');
        if (contentTestForm) {
            contentTestForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.testContentExtraction();
            });
        }

        // 完整流程测试
        const fullTestForm = document.getElementById('full-test-form');
        if (fullTestForm) {
            fullTestForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.testFullExtraction();
            });
        }
    }

    // 绑定站点表单事件
    bindSiteFormEvents() {
        const siteForm = document.getElementById('siteForm');
        if (siteForm) {
            siteForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveSite();
            });
        }
    }

    // 绑定筛选条件事件
    bindFilterEvents() {
        // 站点筛选条件
        const filterElements = [
            'filter-site-name',
            'filter-site-url',
            'filter-province',
            'filter-city',
            'filter-info-type',
            'filter-status',
            'filter-is-test'
        ];

        filterElements.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (element) {
                // 输入框使用input事件（实时搜索）
                if (element.type === 'text') {
                    element.addEventListener('input', () => {
                        // 防抖处理，避免频繁请求
                        clearTimeout(this.filterTimeout);
                        this.filterTimeout = setTimeout(() => {
                            this.loadSites(1);
                        }, 500);
                    });
                } else {
                    // 下拉框使用change事件
                    element.addEventListener('change', () => {
                        this.loadSites(1);
                    });
                }
            }
        });
    }

    // 显示消息提示
    showMessage(message, type = 'info') {
        const alertClass = `alert-${type}`;
        const alertHTML = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // 在页面顶部显示消息
        const container = document.querySelector('.container-fluid');
        const alertDiv = document.createElement('div');
        alertDiv.innerHTML = alertHTML;
        container.insertBefore(alertDiv, container.firstChild);
        
        // 3秒后自动消失
        setTimeout(() => {
            const alert = alertDiv.querySelector('.alert');
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 3000);
    }

    // 显示加载状态
    showLoading(containerId) {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = `
                <div class="loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">加载中...</p>
                </div>
            `;
        }
    }

    // 显示空状态
    showEmpty(containerId, message = '暂无数据') {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="bi bi-inbox"></i>
                    <p>${message}</p>
                </div>
            `;
        }
    }

    // 格式化日期
    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    }

    // 格式化最新运行时间
    formatLastRunTime(dateString) {
        if (!dateString) {
            return '<span class="text-muted">未运行</span>';
        }

        const runTime = new Date(dateString);
        const now = new Date();
        const diffMs = now - runTime;
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        let timeAgo = '';
        let badgeClass = '';

        if (diffMinutes < 1) {
            timeAgo = '刚刚';
            badgeClass = 'bg-success';
        } else if (diffMinutes < 60) {
            timeAgo = `${diffMinutes}分钟前`;
            badgeClass = 'bg-success';
        } else if (diffHours < 24) {
            timeAgo = `${diffHours}小时前`;
            badgeClass = diffHours < 6 ? 'bg-success' : 'bg-warning';
        } else if (diffDays < 7) {
            timeAgo = `${diffDays}天前`;
            badgeClass = 'bg-warning';
        } else {
            timeAgo = runTime.toLocaleDateString('zh-CN');
            badgeClass = 'bg-secondary';
        }

        const fullTime = runTime.toLocaleString('zh-CN');
        return `<span class="badge ${badgeClass}" title="${fullTime}">${timeAgo}</span>`;
    }

    // 格式化状态
    formatStatus(status, type = 'task') {
        const statusConfig = type === 'task' ? CONFIG.TASK_STATUS : CONFIG.PUBLISH_STATUS;
        const config = statusConfig[status] || { label: status, class: 'secondary' };
        return `<span class="badge bg-${config.class}">${config.label}</span>`;
    }

    // 加载首页统计数据
    async loadDashboard() {
        try {
            console.log('开始加载统计数据...');
            const result = await api.getStatistics();
            console.log('统计数据结果:', result);

            if (result.success) {
                const data = result.data;
                console.log('统计数据:', data);

                // 更新统计数字
                const totalSitesElement = document.getElementById('total-sites');
                const todayCollectedElement = document.getElementById('today-collected');
                const todayUncollectedElement = document.getElementById('today-uncollected');
                const successRateElement = document.getElementById('success-rate');

                if (totalSitesElement) {
                    totalSitesElement.textContent = data.total_sites || 0;
                    console.log('更新总站点数:', data.total_sites);
                }

                if (todayCollectedElement) {
                    todayCollectedElement.textContent = data.today_collected || 0;
                    console.log('更新今日已采集:', data.today_collected);
                }

                if (todayUncollectedElement) {
                    todayUncollectedElement.textContent = data.today_uncollected || 0;
                    console.log('更新今日未采集:', data.today_uncollected);
                }

                // 计算成功率
                const collected = data.today_collected || 0;
                const failed = data.today_failed || 0;
                const total = collected + failed;
                const successRate = total > 0 ? Math.round((collected / total) * 100) : 0;

                if (successRateElement) {
                    successRateElement.textContent = `${successRate}%`;
                    console.log('更新成功率:', successRate);
                }

                console.log('统计数据更新完成');
            } else {
                console.error('获取统计数据失败:', result.message);
                this.showMessage(result.message, 'error');
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
            this.showMessage('加载统计数据失败', 'error');
        }
    }

    // 重置筛选条件
    resetFilters() {
        document.getElementById('filter-site-name').value = '';
        document.getElementById('filter-site-url').value = '';
        document.getElementById('filter-province').value = '';
        document.getElementById('filter-city').value = '';
        document.getElementById('filter-info-type').value = '';
        document.getElementById('filter-status').value = '';
        document.getElementById('filter-is-test').value = '';

        // 重置后重新加载数据
        this.loadSites(1);
    }

    // 加载招标站点列表
    async loadSites(page = 1) {
        this.showLoading('sites-table-body');
        
        try {
            // 获取筛选条件
            const filters = {
                page: page,
                page_size: CONFIG.DEFAULT_PAGE_SIZE
            };
            
            const siteName = document.getElementById('filter-site-name')?.value;
            if (siteName) filters.site_name = siteName;

            const siteUrl = document.getElementById('filter-site-url')?.value;
            if (siteUrl) filters.site_url = siteUrl;

            const province = document.getElementById('filter-province')?.value;
            if (province) filters.province = province;

            const city = document.getElementById('filter-city')?.value;
            if (city) filters.city = city;

            const infoType = document.getElementById('filter-info-type')?.value;
            if (infoType) filters.info_type = infoType;

            const status = document.getElementById('filter-status')?.value;
            if (status !== '') {
                filters.status = status;
            } else {
                // 默认只显示启用状态的站点（排除已删除的站点）
                filters.status = 1;
            }

            const isTest = document.getElementById('filter-is-test')?.value;
            if (isTest !== '') filters.is_test = isTest;

            const result = await api.getBidSites(filters);
            
            if (result.success) {
                this.renderSitesTable(result.data.data);
                this.renderSitesPagination(result.data);
                this.currentSitePage = page;
            } else {
                this.showEmpty('sites-table-body', result.message);
                this.showMessage(result.message, 'error');
            }
        } catch (error) {
            console.error('加载站点列表失败:', error);
            this.showEmpty('sites-table-body', '加载失败');
            this.showMessage('加载站点列表失败', 'error');
        }
    }

    // 渲染站点表格
    renderSitesTable(sites) {
        const tbody = document.getElementById('sites-table-body');
        
        if (!sites || sites.length === 0) {
            this.showEmpty('sites-table-body');
            return;
        }

        const html = sites.map(site => `
            <tr>
                <td>
                    <div class="fw-bold">${site.site_name}</div>
                    <div class="d-md-none">
                        <small class="text-muted">${site.province || ''} ${site.city || ''}</small>
                        <br><small class="text-muted">${this.formatDate(site.created_at)}</small>
                    </div>
                </td>
                <td class="d-none d-md-table-cell">
                    <a href="${site.site_url}" target="_blank" class="text-decoration-none">
                        ${site.site_url.length > 40 ? site.site_url.substring(0, 40) + '...' : site.site_url}
                    </a>
                </td>
                <td class="d-none d-lg-table-cell">${site.province || '-'}</td>
                <td class="d-none d-lg-table-cell">${site.city || '-'}</td>
                <td class="d-none d-xl-table-cell">${site.added_by || '-'}</td>
                <td>
                    <span class="badge ${site.info_type === '拍卖' ? 'bg-info' : 'bg-primary'}">${site.info_type || '招标'}</span>
                </td>
                <td>
                    ${site.status === 1 ? '<span class="badge bg-success">启用</span>' : '<span class="badge bg-secondary">禁用</span>'}
                </td>
                <td>
                    ${site.is_test ? '<span class="badge bg-warning">测试</span>' : '<span class="badge bg-light text-dark">正式</span>'}
                </td>
                <td class="d-none d-lg-table-cell">${this.formatLastRunTime(site.last_run_time)}</td>
                <td class="d-none d-md-table-cell">${this.formatDate(site.created_at)}</td>
                <td class="table-actions">
                    <div class="btn-group-vertical btn-group-sm d-md-none" role="group">
                        <button class="btn btn-outline-primary btn-sm" onclick="app.editSite(${site.id})" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="app.configRules(${site.id})" title="配置规则">
                            <i class="bi bi-gear"></i>
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="app.testSite(${site.id})" title="测试采集">
                            <i class="bi bi-play"></i>
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="app.simulateRun(${site.id})" title="模拟运行">
                            <i class="bi bi-clock"></i>
                        </button>
                    </div>
                    <div class="d-none d-md-block">
                        <button class="btn btn-sm btn-primary me-1" onclick="app.editSite(${site.id})" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm btn-info me-1" onclick="app.configRules(${site.id})" title="配置规则">
                            <i class="bi bi-gear"></i>
                        </button>
                        <button class="btn btn-sm btn-success me-1" onclick="app.testSite(${site.id})" title="测试采集">
                            <i class="bi bi-play"></i>
                        </button>
                        <button class="btn btn-sm btn-warning me-1" onclick="app.simulateRun(${site.id})" title="模拟运行">
                            <i class="bi bi-clock"></i>
                        </button>
                        <button class="btn btn-sm btn-warning me-1" onclick="app.viewResults(${site.id})" title="查看结果">
                            <i class="bi bi-file-text"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="app.deleteSite(${site.id})" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        tbody.innerHTML = html;
    }

    // 渲染分页
    renderSitesPagination(data) {
        const pagination = document.getElementById('sites-pagination');
        const { page, total_pages } = data;
        
        if (total_pages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let html = '';
        
        // 上一页
        if (page > 1) {
            html += `<li class="page-item">
                <a class="page-link" href="#" onclick="app.loadSites(${page - 1})">上一页</a>
            </li>`;
        }

        // 页码
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(total_pages, page + 2);
        
        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === page ? 'active' : '';
            html += `<li class="page-item ${activeClass}">
                <a class="page-link" href="#" onclick="app.loadSites(${i})">${i}</a>
            </li>`;
        }

        // 下一页
        if (page < total_pages) {
            html += `<li class="page-item">
                <a class="page-link" href="#" onclick="app.loadSites(${page + 1})">下一页</a>
            </li>`;
        }

        pagination.innerHTML = html;
    }

    // 测试完整采集流程
    async testFullExtraction() {
        const listUrl = document.getElementById('full-test-list-url').value;
        const maxLinks = parseInt(document.getElementById('full-test-max-links').value);
        const linkType = document.getElementById('full-link-extract-type').value;
        const linkRule = document.getElementById('full-link-extract-rule').value;
        const titleType = document.getElementById('full-title-extract-type').value;
        const titleRule = document.getElementById('full-title-extract-rule').value;
        const contentType = document.getElementById('full-content-extract-type').value;
        const contentRule = document.getElementById('full-content-extract-rule').value;

        if (!listUrl || !linkType || !linkRule || !titleType || !titleRule || !contentType || !contentRule) {
            this.showMessage('请填写完整的测试参数', 'warning');
            return;
        }

        const resultDiv = document.getElementById('full-test-result');
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>正在执行完整测试，请稍候...</p></div>';

        try {
            const result = await api.testFullExtraction({
                list_url: listUrl,
                link_extract_type: linkType,
                link_extract_rule: linkRule,
                title_extract_type: titleType,
                title_extract_rule: titleRule,
                content_extract_type: contentType,
                content_extract_rule: contentRule,
                max_links: maxLinks
            });

            if (result.success) {
                const data = result.data;
                let html = `
                    <div class="test-result success">
                        <h6><i class="bi bi-check-circle"></i> 完整测试成功</h6>
                        <p><strong>总链接数：</strong>${data.total_links}，<strong>处理链接数：</strong>${data.processed_links}</p>
                        <div class="mt-3">
                `;

                if (data.results && data.results.length > 0) {
                    data.results.forEach((item, index) => {
                        const hasError = item.error ? 'border-danger' : 'border-success';
                        html += `
                            <div class="card mb-2 ${hasError}">
                                <div class="card-header">
                                    <small><strong>链接 ${index + 1}:</strong> ${item.url}</small>
                                </div>
                                <div class="card-body">
                                    ${item.error ?
                                        `<div class="text-danger">错误: ${item.error}</div>` :
                                        `<div><strong>标题:</strong> ${item.title || '未提取到标题'}</div>
                                         <div><strong>内容:</strong> ${item.content ? (item.content.length > 200 ? item.content.substring(0, 200) + '...' : item.content) : '未提取到内容'}</div>`
                                    }
                                </div>
                            </div>
                        `;
                    });
                } else {
                    html += '<p>未获取到任何结果</p>';
                }

                html += '</div></div>';
                resultDiv.innerHTML = html;
            } else {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        <h6><i class="bi bi-x-circle"></i> 测试失败</h6>
                        <p>${result.message}</p>
                    </div>
                `;
            }
        } catch (error) {
            console.error('完整测试失败:', error);
            resultDiv.innerHTML = `
                <div class="test-result error">
                    <h6><i class="bi bi-x-circle"></i> 测试失败</h6>
                    <p>网络请求失败</p>
                </div>
            `;
        }
    }

    // 站点操作方法
    editSite(id) {
        this.showSiteForm(id);
    }

    configRules(id) {
        this.showRulesModal(id);
    }

    async testSite(id) {
        try {
            const result = await api.createCollectionTask({ site_id: id });
            if (result.success) {
                this.showMessage('采集任务已创建', 'success');
            } else {
                this.showMessage(result.message, 'error');
            }
        } catch (error) {
            console.error('创建采集任务失败:', error);
            this.showMessage('创建采集任务失败', 'error');
        }
    }

    viewResults(id) {
        // 切换到结果页面并筛选该站点的结果
        showPage('results');
        // 这里可以设置筛选条件
    }

    async deleteSite(id) {
        if (confirm('确定要删除这个招标站点吗？')) {
            try {
                const result = await api.deleteBidSite(id);
                if (result.success) {
                    this.showMessage('删除成功', 'success');
                    this.loadSites(this.currentSitePage);
                } else {
                    this.showMessage(result.message, 'error');
                }
            } catch (error) {
                console.error('删除站点失败:', error);
                this.showMessage('删除站点失败', 'error');
            }
        }
    }

    // 显示规则配置模态框
    showRulesModal(siteId) {
        const modal = new bootstrap.Modal(document.getElementById('rulesModal'));
        document.getElementById('rulesSiteId').value = siteId;
        // 清空测试结果
        const resultDiv = document.getElementById('rules-test-result');
        if (resultDiv) {
            resultDiv.style.display = 'none';
            resultDiv.innerHTML = '';
        }
        // 清空测试URL
        document.getElementById('rulesTestUrl').value = '';
        document.getElementById('rulesContentTestUrl').value = '';
        this.loadRulesData(siteId);
        modal.show();
    }

    // 加载规则数据
    async loadRulesData(siteId) {
        try {
            const result = await api.getCollectionRule(siteId);
            if (result.success) {
                const rules = result.data;
                document.getElementById('rulesLinkType').value = rules.link_extract_type || '';
                document.getElementById('rulesLinkRule').value = rules.link_extract_rule || '';
                document.getElementById('rulesTitleType').value = rules.title_extract_type || '';
                document.getElementById('rulesTitleRule').value = rules.title_extract_rule || '';
                document.getElementById('rulesContentType').value = rules.content_extract_type || '';
                document.getElementById('rulesContentRule').value = rules.content_extract_rule || '';
            } else {
                // 如果没有规则，清空表单
                this.clearRulesForm();
            }
        } catch (error) {
            console.error('加载规则数据失败:', error);
            this.clearRulesForm();
        }
    }

    // 清空规则表单
    clearRulesForm() {
        document.getElementById('rulesLinkType').value = '';
        document.getElementById('rulesLinkRule').value = '';
        document.getElementById('rulesTitleType').value = '';
        document.getElementById('rulesTitleRule').value = '';
        document.getElementById('rulesContentType').value = '';
        document.getElementById('rulesContentRule').value = '';
        document.getElementById('rulesTestUrl').value = '';
        document.getElementById('rulesContentTestUrl').value = '';
    }

    // 保存规则
    async saveRules() {
        try {
            const siteId = document.getElementById('rulesSiteId').value;
            const data = {
                link_extract_type: document.getElementById('rulesLinkType').value,
                link_extract_rule: document.getElementById('rulesLinkRule').value,
                title_extract_type: document.getElementById('rulesTitleType').value,
                title_extract_rule: document.getElementById('rulesTitleRule').value,
                content_extract_type: document.getElementById('rulesContentType').value,
                content_extract_rule: document.getElementById('rulesContentRule').value
            };

            const result = await api.saveCollectionRule(siteId, data);
            if (result.success) {
                this.showMessage('规则保存成功', 'success');
                bootstrap.Modal.getInstance(document.getElementById('rulesModal')).hide();
            } else {
                this.showMessage(result.message, 'error');
            }
        } catch (error) {
            console.error('保存规则失败:', error);
            this.showMessage('保存规则失败', 'error');
        }
    }

    // 加载采集结果
    async loadResults(page = 1) {
        this.showLoading('results-table-body');

        try {
            // 获取筛选条件
            const filters = {
                page: page,
                page_size: CONFIG.DEFAULT_PAGE_SIZE
            };

            const siteName = document.getElementById('filter-result-site')?.value;
            if (siteName) filters.site_name = siteName;

            const province = document.getElementById('filter-result-province')?.value;
            if (province) filters.province = province;

            const publishStatus = document.getElementById('filter-publish-status')?.value;
            if (publishStatus) filters.publish_status = publishStatus;

            const startDate = document.getElementById('filter-start-date')?.value;
            if (startDate) filters.start_date = startDate;

            const endDate = document.getElementById('filter-end-date')?.value;
            if (endDate) filters.end_date = endDate;

            const keyword = document.getElementById('filter-keyword')?.value;
            if (keyword) filters.keyword = keyword;

            const result = await api.getCollectionResults(filters);

            if (result.success) {
                this.renderResultsTable(result.data.data);
                this.renderResultsPagination(result.data);
                this.currentResultPage = page;
            } else {
                this.showEmpty('results-table-body', result.message);
                this.showMessage(result.message, 'error');
            }
        } catch (error) {
            console.error('加载采集结果失败:', error);
            this.showEmpty('results-table-body', '加载失败');
            this.showMessage('加载采集结果失败', 'error');
        }
    }

    // 渲染结果表格
    renderResultsTable(results) {
        const tbody = document.getElementById('results-table-body');

        if (!results || results.length === 0) {
            this.showEmpty('results-table-body');
            return;
        }

        const html = results.map(result => `
            <tr>
                <td>
                    <div class="fw-bold">${result.site_name}</div>
                    <div class="d-lg-none">
                        <small class="text-muted">${result.province || ''}</small>
                    </div>
                </td>
                <td>
                    <a href="${result.bid_url}" target="_blank" class="text-decoration-none">
                        ${result.bid_title.length > 60 ? result.bid_title.substring(0, 60) + '...' : result.bid_title}
                    </a>
                    <div class="d-md-none">
                        <small class="text-muted">${this.formatDate(result.collected_at)}</small>
                    </div>
                </td>
                <td class="d-none d-lg-table-cell">${result.province || '-'}</td>
                <td>${this.formatStatus(result.publish_status, 'publish')}</td>
                <td class="d-none d-md-table-cell">${this.formatDate(result.collected_at)}</td>
                <td class="table-actions">
                    <div class="btn-group btn-group-sm" role="group">
                        <button class="btn btn-outline-info" onclick="app.viewResultDetail(${result.id})" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-${result.publish_status === 'published' ? 'warning' : 'success'}"
                                onclick="app.togglePublishStatus(${result.id}, '${result.publish_status}')"
                                title="${result.publish_status === 'published' ? '取消发布' : '发布'}">
                            <i class="bi bi-${result.publish_status === 'published' ? 'x-circle' : 'check-circle'}"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        tbody.innerHTML = html;
    }

    // 渲染结果分页
    renderResultsPagination(data) {
        const pagination = document.getElementById('results-pagination');
        const { page, total_pages } = data;

        if (total_pages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let html = '';

        // 上一页
        if (page > 1) {
            html += `<li class="page-item">
                <a class="page-link" href="#" onclick="app.loadResults(${page - 1})">上一页</a>
            </li>`;
        }

        // 页码
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(total_pages, page + 2);

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === page ? 'active' : '';
            html += `<li class="page-item ${activeClass}">
                <a class="page-link" href="#" onclick="app.loadResults(${i})">${i}</a>
            </li>`;
        }

        // 下一页
        if (page < total_pages) {
            html += `<li class="page-item">
                <a class="page-link" href="#" onclick="app.loadResults(${page + 1})">下一页</a>
            </li>`;
        }

        pagination.innerHTML = html;
    }

    // 查看结果详情
    async viewResultDetail(resultId) {
        try {
            const result = await api.getCollectionResult(resultId);
            if (result.success) {
                const data = result.data;
                const html = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>基本信息</h6>
                            <table class="table table-sm">
                                <tr><td><strong>站点名称:</strong></td><td>${data.site_name}</td></tr>
                                <tr><td><strong>站点URL:</strong></td><td><a href="${data.site_url}" target="_blank">${data.site_url}</a></td></tr>
                                <tr><td><strong>省份:</strong></td><td>${data.province || '-'}</td></tr>
                                <tr><td><strong>城市:</strong></td><td>${data.city || '-'}</td></tr>
                                <tr><td><strong>采集时间:</strong></td><td>${this.formatDate(data.collected_at)}</td></tr>
                                <tr><td><strong>发布状态:</strong></td><td>${this.formatStatus(data.publish_status, 'publish')}</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>招标信息</h6>
                            <p><strong>招标链接:</strong></p>
                            <p><a href="${data.bid_url}" target="_blank">${data.bid_url}</a></p>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <h6>招标标题</h6>
                            <div class="code-block">${data.bid_title}</div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <h6>招标内容</h6>
                            <div class="code-block" style="max-height: 400px; overflow-y: auto;">${data.bid_content}</div>
                        </div>
                    </div>
                `;

                document.getElementById('resultDetail').innerHTML = html;
                const modal = new bootstrap.Modal(document.getElementById('resultModal'));
                modal.show();
            } else {
                this.showMessage(result.message, 'error');
            }
        } catch (error) {
            console.error('获取结果详情失败:', error);
            this.showMessage('获取结果详情失败', 'error');
        }
    }

    // 切换发布状态
    async togglePublishStatus(resultId, currentStatus) {
        const newStatus = currentStatus === 'published' ? 'unpublished' : 'published';
        const action = newStatus === 'published' ? '发布' : '取消发布';

        if (confirm(`确定要${action}这条记录吗？`)) {
            try {
                const result = await api.updatePublishStatus(resultId, newStatus);
                if (result.success) {
                    this.showMessage(`${action}成功`, 'success');
                    this.loadResults(this.currentResultPage);
                } else {
                    this.showMessage(result.message, 'error');
                }
            } catch (error) {
                console.error('更新发布状态失败:', error);
                this.showMessage('更新发布状态失败', 'error');
            }
        }
    }

    // 加载测试页面
    loadTestPage() {
        // 测试页面已经在HTML中定义，这里可以做一些初始化工作
        console.log('测试页面已加载');
    }

    // 系统状态检查
    async checkSystemHealth() {
        try {
            const result = await api.healthCheck();
            if (result.success) {
                this.showMessage('系统运行正常', 'success');
            } else {
                this.showMessage('系统状态异常', 'warning');
            }
        } catch (error) {
            this.showMessage('无法连接到服务器', 'error');
        }
    }

    // 模拟站点运行
    async simulateRun(siteId) {
        try {
            const response = await fetch(`/api/bid_sites/${siteId}/run`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            if (result.code === 200) {
                this.showMessage('运行时间更新成功', 'success');
                // 重新加载站点列表以显示最新的运行时间
                this.loadSites();
            } else {
                this.showMessage(result.message || '更新失败', 'error');
            }
        } catch (error) {
            console.error('模拟运行失败:', error);
            this.showMessage('模拟运行失败', 'error');
        }
    }

    // HTML转义函数
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 复制到剪贴板
    async copyToClipboard(elementId) {
        try {
            const element = document.getElementById(elementId);
            if (element) {
                const text = element.textContent || element.innerText;
                await navigator.clipboard.writeText(text);
                this.showMessage('已复制到剪贴板', 'success');
            } else {
                this.showMessage('未找到要复制的内容', 'error');
            }
        } catch (error) {
            console.error('复制失败:', error);
            this.showMessage('复制失败，请手动选择复制', 'error');
        }
    }

    // 测试单个规则步骤（在规则配置模态框中使用）
    async testRuleStep(stepType) {
        try {
            const siteId = document.getElementById('rulesSiteId').value;
            if (!siteId) {
                this.showMessage('请先选择一个站点', 'warning');
                return;
            }

            // 获取站点信息
            const siteResult = await api.getBidSite(siteId);
            if (!siteResult.success) {
                this.showMessage('获取站点信息失败', 'error');
                return;
            }

            const site = siteResult.data;
            const resultDiv = document.getElementById('rules-test-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>正在测试...</p></div>';

            // 获取测试URL，如果没有指定则使用站点默认URL
            const testUrl = document.getElementById('rulesTestUrl').value || site.site_url;

            if (stepType === 'link') {
                // 测试链接提取
                const extractType = document.getElementById('rulesLinkType').value;
                const extractRule = document.getElementById('rulesLinkRule').value;

                if (!extractType || !extractRule) {
                    this.showMessage('请填写链接提取规则', 'warning');
                    return;
                }

                const result = await api.testLinkExtraction({
                    url: testUrl,
                    extract_type: extractType,
                    extract_rule: extractRule
                });

                if (result.success) {
                    const data = result.data;
                    let linksHtml = '';
                    if (data.links && data.links.length > 0) {
                        data.links.forEach((link, index) => {
                            linksHtml += `${index + 1}. <a href="${link}" target="_blank">${link}</a><br>`;
                        });
                    } else {
                        linksHtml = '<span class="text-muted">未提取到任何链接</span>';
                    }

                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h6><i class="bi bi-check-circle"></i> 链接提取测试成功</h6>
                            <p><strong>提取到 ${data.total_count} 个链接：</strong></p>
                            <div class="border p-3 bg-light" style="max-height: 300px; overflow-y: auto;">
                                ${linksHtml}
                            </div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h6><i class="bi bi-x-circle"></i> 链接提取测试失败</h6>
                            <p>${result.message}</p>
                        </div>
                    `;
                }

            } else if (stepType === 'content') {
                // 测试内容提取
                const titleType = document.getElementById('rulesTitleType').value;
                const titleRule = document.getElementById('rulesTitleRule').value;
                const contentType = document.getElementById('rulesContentType').value;
                const contentRule = document.getElementById('rulesContentRule').value;
                const contentTestUrl = document.getElementById('rulesContentTestUrl').value;

                if (!titleType || !titleRule || !contentType || !contentRule) {
                    this.showMessage('请填写完整的内容提取规则', 'warning');
                    return;
                }

                if (!contentTestUrl) {
                    this.showMessage('请填写内容测试URL', 'warning');
                    return;
                }

                // 直接使用指定的URL测试内容提取
                const contentResult = await api.testContentExtraction({
                    url: contentTestUrl,
                    title_extract_type: titleType,
                    title_extract_rule: titleRule,
                    content_extract_type: contentType,
                    content_extract_rule: contentRule
                });

                if (contentResult.success) {
                    const data = contentResult.data;
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h6><i class="bi bi-check-circle"></i> 内容提取测试成功</h6>
                            <p><strong>测试URL：</strong><a href="${contentTestUrl}" target="_blank">${contentTestUrl}</a></p>

                            <!-- 导航标签 -->
                            <ul class="nav nav-tabs mt-3" id="content-result-tabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="content-extracted-tab" data-bs-toggle="tab" data-bs-target="#content-extracted" type="button" role="tab">
                                        <i class="bi bi-check-circle"></i> 提取结果
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="content-html-tab" data-bs-toggle="tab" data-bs-target="#content-html" type="button" role="tab">
                                        <i class="bi bi-code-slash"></i> HTML源码
                                    </button>
                                </li>
                            </ul>

                            <!-- 标签内容 -->
                            <div class="tab-content mt-3" id="content-result-tab-content">
                                <!-- 提取结果标签页 -->
                                <div class="tab-pane fade show active" id="content-extracted" role="tabpanel">
                                    <div class="mb-3">
                                        <strong>提取的标题：</strong>
                                        <div class="border p-2 bg-light">${data.title || '<span class="text-muted">未提取到标题</span>'}</div>
                                    </div>
                                    <div class="mb-3">
                                        <strong>提取的内容：</strong>
                                        <div class="border p-2 bg-light" style="max-height: 200px; overflow-y: auto;">
                                            ${data.content || '<span class="text-muted">未提取到内容</span>'}
                                        </div>
                                    </div>
                                </div>

                                <!-- HTML源码标签页 -->
                                <div class="tab-pane fade" id="content-html" role="tabpanel">
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <strong>页面HTML源码：</strong>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="app.copyToClipboard('content-html-source')">
                                                <i class="bi bi-clipboard"></i> 复制
                                            </button>
                                        </div>
                                        <pre class="code-block" id="content-html-source" style="max-height: 400px; overflow: auto; font-size: 12px; line-height: 1.4;">
                                            <code class="language-html">${this.escapeHtml(data.html || '无HTML内容')}</code>
                                        </pre>
                                    </div>
                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle"></i>
                                        <strong>提示：</strong>查看HTML源码可以帮助您更好地编写提取规则。
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h6><i class="bi bi-x-circle"></i> 内容提取测试失败</h6>
                            <p><strong>测试URL：</strong><a href="${contentTestUrl}" target="_blank">${contentTestUrl}</a></p>
                            <p><strong>错误信息：</strong>${contentResult.message}</p>
                        </div>
                    `;
                }
            }

        } catch (error) {
            console.error('测试规则步骤失败:', error);
            this.showMessage('测试失败', 'error');
            const resultDiv = document.getElementById('rules-test-result');
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="bi bi-x-circle"></i> 测试失败</h6>
                    <p>发生了未知错误，请检查网络连接和服务器状态</p>
                </div>
            `;
        }
    }

    // 测试规则完整流程（在规则配置模态框中使用）
    async testRulesFull() {
        try {
            const siteId = document.getElementById('rulesSiteId').value;
            if (!siteId) {
                this.showMessage('请先选择一个站点', 'warning');
                return;
            }

            // 获取站点信息
            const siteResult = await api.getBidSite(siteId);
            if (!siteResult.success) {
                this.showMessage('获取站点信息失败', 'error');
                return;
            }

            const site = siteResult.data;
            // 获取测试URL，如果没有指定则使用站点默认URL
            const listUrl = document.getElementById('rulesTestUrl').value || site.site_url;
            const linkType = document.getElementById('rulesLinkType').value;
            const linkRule = document.getElementById('rulesLinkRule').value;
            const titleType = document.getElementById('rulesTitleType').value;
            const titleRule = document.getElementById('rulesTitleRule').value;
            const contentType = document.getElementById('rulesContentType').value;
            const contentRule = document.getElementById('rulesContentRule').value;

            if (!linkType || !linkRule || !titleType || !titleRule || !contentType || !contentRule) {
                this.showMessage('请填写完整的采集规则', 'warning');
                return;
            }

            const resultDiv = document.getElementById('rules-test-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>正在测试完整采集流程...</p></div>';

            const result = await api.testFullExtraction({
                list_url: listUrl,
                link_extract_type: linkType,
                link_extract_rule: linkRule,
                title_extract_type: titleType,
                title_extract_rule: titleRule,
                content_extract_type: contentType,
                content_extract_rule: contentRule,
                max_links: 3
            });

            if (result.success) {
                const data = result.data;
                let html = `
                    <div class="test-result success">
                        <h6><i class="bi bi-check-circle"></i> 规则测试成功</h6>
                        <p><strong>总链接数：</strong>${data.total_links}，<strong>处理链接数：</strong>${data.processed_links}</p>
                        <div class="mt-3">
                `;

                if (data.results && data.results.length > 0) {
                    data.results.forEach((item, index) => {
                        const hasError = item.error ? 'border-danger' : 'border-success';
                        html += `
                            <div class="card mb-2 ${hasError}">
                                <div class="card-header">
                                    <small><strong>链接 ${index + 1}:</strong> ${item.url}</small>
                                </div>
                                <div class="card-body">
                                    ${item.error ?
                                        `<div class="text-danger">错误: ${item.error}</div>` :
                                        `<div><strong>标题:</strong> ${item.title || '未提取到标题'}</div>
                                         <div><strong>内容:</strong> ${item.content ? (item.content.length > 200 ? item.content.substring(0, 200) + '...' : item.content) : '未提取到内容'}</div>`
                                    }
                                </div>
                            </div>
                        `;
                    });
                } else {
                    html += '<p>未获取到任何结果</p>';
                }

                html += '</div></div>';
                resultDiv.innerHTML = html;
            } else {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        <h6><i class="bi bi-x-circle"></i> 规则测试失败</h6>
                        <p>${result.message}</p>
                    </div>
                `;
            }
        } catch (error) {
            console.error('规则测试失败:', error);
            const resultDiv = document.getElementById('rules-test-result');
            resultDiv.innerHTML = `
                <div class="test-result error">
                    <h6><i class="bi bi-x-circle"></i> 规则测试失败</h6>
                    <p>网络请求失败</p>
                </div>
            `;
        }
    }

    showSiteForm(siteId = null) {
        const modal = new bootstrap.Modal(document.getElementById('siteModal'));
        const title = document.getElementById('siteModalTitle');

        if (siteId) {
            title.textContent = '编辑招标站点';
            this.loadSiteData(siteId);
        } else {
            title.textContent = '添加招标站点';
            this.clearSiteForm();
        }

        modal.show();
    }

    // 清空站点表单
    clearSiteForm() {
        document.getElementById('siteId').value = '';
        document.getElementById('siteName').value = '';
        document.getElementById('siteUrl').value = '';
        document.getElementById('siteProvince').value = '';
        document.getElementById('siteCity').value = '';
        document.getElementById('siteAddedBy').value = '';
        document.getElementById('siteSourceId').value = '';
        document.getElementById('siteInfoType').value = '招标';
        document.getElementById('siteStatus').value = '1';
        document.getElementById('siteIsTest').checked = false;
        document.getElementById('siteUrlWhitelist').value = '';
        document.getElementById('siteUrlBlacklist').value = '';
        document.getElementById('siteCollectKeywords').value = '';
        document.getElementById('siteBlockKeywords').value = '';
    }

    // 加载站点数据
    async loadSiteData(siteId) {
        try {
            const result = await api.getBidSite(siteId);
            if (result.success) {
                const site = result.data;
                document.getElementById('siteId').value = site.id;
                document.getElementById('siteName').value = site.site_name || '';
                document.getElementById('siteUrl').value = site.site_url || '';
                document.getElementById('siteProvince').value = site.province || '';
                document.getElementById('siteCity').value = site.city || '';
                document.getElementById('siteAddedBy').value = site.added_by || '';
                document.getElementById('siteSourceId').value = site.source_id || '';
                document.getElementById('siteInfoType').value = site.info_type || '招标';
                document.getElementById('siteStatus').value = site.status !== undefined ? site.status.toString() : '1';
                document.getElementById('siteIsTest').checked = site.is_test == 1;
                document.getElementById('siteUrlWhitelist').value = site.url_whitelist || '';
                document.getElementById('siteUrlBlacklist').value = site.url_blacklist || '';
                document.getElementById('siteCollectKeywords').value = site.collect_keywords || '';
                document.getElementById('siteBlockKeywords').value = site.block_keywords || '';
            } else {
                this.showMessage(result.message, 'error');
            }
        } catch (error) {
            console.error('加载站点数据失败:', error);
            this.showMessage('加载站点数据失败', 'error');
        }
    }

    // 保存站点
    async saveSite() {
        try {
            const siteId = document.getElementById('siteId').value;
            const data = {
                site_name: document.getElementById('siteName').value,
                site_url: document.getElementById('siteUrl').value,
                province: document.getElementById('siteProvince').value,
                city: document.getElementById('siteCity').value,
                added_by: document.getElementById('siteAddedBy').value,
                source_id: document.getElementById('siteSourceId').value,
                info_type: document.getElementById('siteInfoType').value,
                status: parseInt(document.getElementById('siteStatus').value),
                is_test: document.getElementById('siteIsTest').checked ? 1 : 0,
                url_whitelist: document.getElementById('siteUrlWhitelist').value,
                url_blacklist: document.getElementById('siteUrlBlacklist').value,
                collect_keywords: document.getElementById('siteCollectKeywords').value,
                block_keywords: document.getElementById('siteBlockKeywords').value
            };

            let result;
            if (siteId) {
                result = await api.updateBidSite(siteId, data);
            } else {
                result = await api.createBidSite(data);
            }

            if (result.success) {
                this.showMessage(siteId ? '更新成功' : '添加成功', 'success');
                bootstrap.Modal.getInstance(document.getElementById('siteModal')).hide();
                this.loadSites(this.currentSitePage);
            } else {
                this.showMessage(result.message, 'error');
            }
        } catch (error) {
            console.error('保存站点失败:', error);
            this.showMessage('保存站点失败', 'error');
        }
    }

    // 测试链接提取
    async testLinkExtraction() {
        const url = document.getElementById('link-test-url').value;
        const extractType = document.getElementById('link-extract-type').value;
        const extractRule = document.getElementById('link-extract-rule').value;

        if (!url || !extractType || !extractRule) {
            this.showMessage('请填写完整的测试参数', 'warning');
            return;
        }

        const resultDiv = document.getElementById('link-test-result');
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>正在测试...</p></div>';

        try {
            const result = await api.testLinkExtraction({
                url: url,
                extract_type: extractType,
                extract_rule: extractRule
            });

            if (result.success) {
                const data = result.data;
                let linksHtml = '';
                if (data.links && data.links.length > 0) {
                    data.links.forEach((link, index) => {
                        linksHtml += `${index + 1}. ${link}\n`;
                    });
                } else {
                    linksHtml = '未提取到任何链接';
                }

                const html = `
                    <div class="test-result success">
                        <h6><i class="bi bi-check-circle"></i> 测试成功</h6>

                        <!-- 导航标签 -->
                        <ul class="nav nav-tabs" id="link-test-tabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="links-tab" data-bs-toggle="tab" data-bs-target="#links-content" type="button" role="tab">
                                    <i class="bi bi-link"></i> 提取结果 (${data.total_count})
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="link-html-tab" data-bs-toggle="tab" data-bs-target="#link-html-content" type="button" role="tab">
                                    <i class="bi bi-code-slash"></i> HTML源码
                                </button>
                            </li>
                        </ul>

                        <!-- 标签内容 -->
                        <div class="tab-content mt-3" id="link-test-tab-content">
                            <!-- 提取结果标签页 -->
                            <div class="tab-pane fade show active" id="links-content" role="tabpanel">
                                <p><strong>提取到 ${data.total_count} 个链接：</strong></p>
                                <div class="code-block" style="max-height: 300px; overflow-y: auto;">
                                    ${linksHtml}
                                </div>
                            </div>

                            <!-- HTML源码标签页 -->
                            <div class="tab-pane fade" id="link-html-content" role="tabpanel">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <strong>页面HTML源码：</strong>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="app.copyToClipboard('link-html-source-code')">
                                            <i class="bi bi-clipboard"></i> 复制
                                        </button>
                                    </div>
                                    <pre class="code-block" id="link-html-source-code" style="max-height: 400px; overflow: auto; font-size: 12px; line-height: 1.4;">
                                        <code class="language-html">${this.escapeHtml(data.html || '无HTML内容')}</code>
                                    </pre>
                                </div>
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle"></i>
                                    <strong>提示：</strong>查看HTML源码可以帮助您更好地编写链接提取规则。
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                resultDiv.innerHTML = html;
            } else {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        <h6><i class="bi bi-x-circle"></i> 测试失败</h6>
                        <p>${result.message}</p>
                    </div>
                `;
            }
        } catch (error) {
            console.error('链接提取测试失败:', error);
            resultDiv.innerHTML = `
                <div class="test-result error">
                    <h6><i class="bi bi-x-circle"></i> 测试失败</h6>
                    <p>网络请求失败</p>
                </div>
            `;
        }
    }

    // 测试内容提取
    async testContentExtraction() {
        const url = document.getElementById('content-test-url').value;
        const titleType = document.getElementById('title-extract-type').value;
        const titleRule = document.getElementById('title-extract-rule').value;
        const contentType = document.getElementById('content-extract-type').value;
        const contentRule = document.getElementById('content-extract-rule').value;

        if (!url || !titleType || !titleRule || !contentType || !contentRule) {
            this.showMessage('请填写完整的测试参数', 'warning');
            return;
        }

        const resultDiv = document.getElementById('content-test-result');
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>正在测试...</p></div>';

        try {
            const result = await api.testContentExtraction({
                url: url,
                title_extract_type: titleType,
                title_extract_rule: titleRule,
                content_extract_type: contentType,
                content_extract_rule: contentRule
            });

            if (result.success) {
                const data = result.data;
                const html = `
                    <div class="test-result success">
                        <h6><i class="bi bi-check-circle"></i> 测试成功</h6>

                        <!-- 导航标签 -->
                        <ul class="nav nav-tabs" id="content-test-tabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="extracted-tab" data-bs-toggle="tab" data-bs-target="#extracted-content" type="button" role="tab">
                                    <i class="bi bi-check-circle"></i> 提取结果
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="html-tab" data-bs-toggle="tab" data-bs-target="#html-content" type="button" role="tab">
                                    <i class="bi bi-code-slash"></i> HTML源码
                                </button>
                            </li>
                        </ul>

                        <!-- 标签内容 -->
                        <div class="tab-content mt-3" id="content-test-tab-content">
                            <!-- 提取结果标签页 -->
                            <div class="tab-pane fade show active" id="extracted-content" role="tabpanel">
                                <div class="mb-3">
                                    <strong>提取的标题：</strong>
                                    <div class="code-block">${data.title || '未提取到标题'}</div>
                                </div>
                                <div class="mb-3">
                                    <strong>提取的内容：</strong>
                                    <div class="code-block" style="max-height: 200px; overflow-y: auto;">
                                        ${data.content || '未提取到内容'}
                                    </div>
                                </div>
                            </div>

                            <!-- HTML源码标签页 -->
                            <div class="tab-pane fade" id="html-content" role="tabpanel">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <strong>页面HTML源码：</strong>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="app.copyToClipboard('html-source-code')">
                                            <i class="bi bi-clipboard"></i> 复制
                                        </button>
                                    </div>
                                    <pre class="code-block" id="html-source-code" style="max-height: 400px; overflow: auto; font-size: 12px; line-height: 1.4;">
                                        <code class="language-html">${this.escapeHtml(data.html || '无HTML内容')}</code>
                                    </pre>
                                </div>
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle"></i>
                                    <strong>提示：</strong>查看HTML源码可以帮助您更好地编写XPath、CSS选择器或正则表达式规则。
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                resultDiv.innerHTML = html;
            } else {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        <h6><i class="bi bi-x-circle"></i> 测试失败</h6>
                        <p>${result.message}</p>
                    </div>
                `;
            }
        } catch (error) {
            console.error('内容提取测试失败:', error);
            resultDiv.innerHTML = `
                <div class="test-result error">
                    <h6><i class="bi bi-x-circle"></i> 测试失败</h6>
                    <p>网络请求失败</p>
                </div>
            `;
        }
    }
}

// 页面切换函数
function showPage(pageName, action = null) {
    // 隐藏所有页面
    document.querySelectorAll('.page-content').forEach(page => {
        page.style.display = 'none';
    });

    // 显示目标页面
    const targetPage = document.getElementById(`${pageName}-page`);
    if (targetPage) {
        targetPage.style.display = 'block';
    }

    // 更新导航状态
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    const activeLink = document.querySelector(`[onclick="showPage('${pageName}')"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }

    // 根据页面执行相应操作
    switch (pageName) {
        case 'dashboard':
            app.loadDashboard();
            break;
        case 'sites':
            app.loadSites();
            if (action === 'add') {
                app.showSiteForm();
            }
            break;
        case 'results':
            app.loadResults();
            break;
        case 'test':
            app.loadTestPage();
            break;
    }

    app.currentPage = pageName;
}

// 初始化应用
const app = new App();
