/* 自定义样式 */
body {
    background-color: #f8f9fa;
    font-size: 14px;
}

.page-content {
    min-height: 600px;
}

/* 容器宽度自适应 */
.container-fluid {
    max-width: 1400px;
    margin: 0 auto;
    padding-left: 15px;
    padding-right: 15px;
}

/* 导航栏自适应 */
.navbar .container-fluid {
    max-width: 1400px;
}

.navbar-brand {
    font-weight: bold;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* 统计卡片特殊样式 */
.card.text-white .card-header {
    background-color: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    font-weight: 600;
}

.card.text-white .card-body {
    color: white;
}

.card.text-white .card-title {
    color: white;
    font-weight: bold;
    margin-bottom: 0;
}

/* 筛选条件样式 */
.filter-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.filter-section .form-control,
.filter-section .form-select {
    border-radius: 6px;
    border: 1px solid #dee2e6;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.filter-section .form-control:focus,
.filter-section .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 筛选按钮组样式 */
.filter-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.filter-buttons .btn {
    border-radius: 6px;
    font-weight: 500;
}

/* 响应式筛选条件 */
@media (max-width: 768px) {
    .filter-section {
        padding: 10px;
    }

    .filter-buttons {
        flex-direction: column;
        gap: 5px;
    }

    .filter-buttons .btn {
        width: 100%;
    }
}

/* HTML源码显示样式 */
.code-block pre {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    margin: 0;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    color: #495057;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.code-block pre code {
    background: none;
    border: none;
    padding: 0;
    font-size: inherit;
    color: inherit;
}

/* 语法高亮 */
.language-html .hljs-tag {
    color: #d73a49;
}

.language-html .hljs-name {
    color: #22863a;
}

.language-html .hljs-attr {
    color: #6f42c1;
}

.language-html .hljs-string {
    color: #032f62;
}

/* 测试结果标签页样式 */
.nav-tabs .nav-link {
    border-radius: 6px 6px 0 0;
    border: 1px solid transparent;
    color: #495057;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    background-color: #f8f9fa;
}

.nav-tabs .nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

.tab-content {
    border: 1px solid #dee2e6;
    border-top: none;
    border-radius: 0 0 6px 6px;
    padding: 15px;
    background-color: #fff;
}

/* 规则测试结果样式 */
#rules-test-result {
    margin-top: 20px;
}

#rules-test-result .test-result {
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

#rules-test-result .test-result.success {
    background-color: #d1edff;
    border: 1px solid #0ea5e9;
}

#rules-test-result .test-result.error {
    background-color: #fee2e2;
    border: 1px solid #ef4444;
}

#rules-test-result .card {
    font-size: 14px;
}

#rules-test-result .card-header {
    background-color: #f8f9fa;
    padding: 8px 12px;
    font-size: 12px;
}

#rules-test-result .card-body {
    padding: 12px;
}

/* 规则配置模态框样式 */
.rule-form-section {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.rule-form-section h6 {
    color: #495057;
    margin-bottom: 15px;
    font-weight: 600;
}

.rule-form-section .btn {
    margin-top: 10px;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
}

.btn {
    border-radius: 0.375rem;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* 状态标签样式 */
.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.status-success {
    background-color: #d1edff;
    color: #0c63e4;
}

.status-warning {
    background-color: #fff3cd;
    color: #856404;
}

.status-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.status-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* 表格操作按钮 */
.table-actions {
    white-space: nowrap;
}

.table-actions .btn {
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
}

/* 表单样式 */
.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.form-control:focus,
.form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 模态框样式 */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

/* 加载动画 */
.loading {
    text-align: center;
    padding: 2rem;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .container-fluid {
        max-width: 100%;
        padding-left: 20px;
        padding-right: 20px;
    }
}

@media (max-width: 992px) {
    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }

    /* 表格在中等屏幕上的调整 */
    .table-responsive {
        font-size: 0.9rem;
    }

    /* 筛选条件在中等屏幕上的调整 */
    .card-body .row .col-md-2,
    .card-body .row .col-md-3,
    .card-body .row .col-md-4,
    .card-body .row .col-md-6 {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 768px) {
    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .btn-sm {
        padding: 0.125rem 0.25rem;
        font-size: 0.75rem;
    }

    .card-body {
        padding: 0.75rem;
    }

    /* 移动端表格操作按钮垂直排列 */
    .table-actions {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .table-actions .btn {
        margin-right: 0;
        margin-bottom: 0;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    /* 移动端统计卡片调整 */
    .card.stat-card .card-body {
        padding: 0.75rem;
        text-align: center;
    }

    .card.stat-card .card-title {
        font-size: 1.5rem;
    }

    /* 移动端表单调整 */
    .modal-dialog {
        margin: 0.5rem;
    }

    .rule-form-section {
        padding: 1rem;
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding-left: 8px;
        padding-right: 8px;
    }

    /* 超小屏幕表格进一步优化 */
    .table-responsive {
        font-size: 0.8rem;
    }

    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
        vertical-align: middle;
    }

    /* 超小屏幕按钮调整 */
    .btn {
        font-size: 0.8rem;
        padding: 0.375rem 0.5rem;
    }

    .btn-sm {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }

    /* 超小屏幕卡片调整 */
    .card-header {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }

    .card-body {
        padding: 0.5rem;
    }

    /* 超小屏幕模态框调整 */
    .modal-dialog {
        margin: 0.25rem;
    }

    .modal-header,
    .modal-footer {
        padding: 0.5rem;
    }

    .modal-body {
        padding: 0.75rem;
    }
}

/* 代码块样式 */
.code-block {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    white-space: pre-wrap;
    word-break: break-all;
}

/* 测试结果样式 */
.test-result {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 0.375rem;
}

.test-result.success {
    background-color: #d1edff;
    border: 1px solid #b6d7ff;
}

.test-result.error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
}

/* 规则配置表单 */
.rule-form-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.rule-form-section h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

/* 统计卡片动画 */
.card.stat-card {
    transition: transform 0.2s ease-in-out;
}

.card.stat-card:hover {
    transform: translateY(-2px);
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 进度条样式 */
.progress {
    height: 0.5rem;
}

/* 标签样式 */
.badge {
    font-size: 0.75rem;
}

/* 分页样式 */
.pagination .page-link {
    color: #0d6efd;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* 表格内容优化 */
.table td {
    vertical-align: middle;
}

.table .fw-bold {
    font-size: 0.9rem;
}

.table small {
    font-size: 0.75rem;
    color: #6c757d;
}

/* 按钮组优化 */
.btn-group-vertical .btn {
    border-radius: 0;
}

.btn-group-vertical .btn:first-child {
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
}

.btn-group-vertical .btn:last-child {
    border-bottom-left-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

/* 导航栏在小屏幕上的优化 */
@media (max-width: 991px) {
    .navbar-nav {
        text-align: center;
    }

    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
    }
}

/* 卡片间距优化 */
.card + .card {
    margin-top: 1rem;
}

/* 表单控件间距 */
.form-control-sm,
.form-select-sm {
    font-size: 0.875rem;
}

/* 测试结果区域优化 */
.test-result {
    border-radius: 0.5rem;
}

.test-result h6 {
    margin-bottom: 0.75rem;
    font-weight: 600;
}

/* 代码块在小屏幕上的优化 */
@media (max-width: 576px) {
    .code-block {
        font-size: 0.75rem;
        padding: 0.75rem;
    }
}

/* 统计卡片在小屏幕上的优化 */
@media (max-width: 768px) {
    .card.stat-card .card-header {
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }

    .card.stat-card .card-title {
        font-size: 1.75rem;
        margin-bottom: 0;
    }
}

/* 分页在小屏幕上的优化 */
@media (max-width: 576px) {
    .pagination {
        justify-content: center;
        flex-wrap: wrap;
    }

    .pagination .page-link {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }
}
