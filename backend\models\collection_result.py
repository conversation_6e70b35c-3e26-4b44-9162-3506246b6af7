from .database import db

class CollectionResult:
    def __init__(self):
        pass
    
    @staticmethod
    def get_all(page=1, page_size=20, filters=None):
        """获取所有采集结果（分页）"""
        offset = (page - 1) * page_size
        
        # 构建查询条件
        where_conditions = ["1=1"]
        params = []
        
        if filters:
            if filters.get('site_name'):
                where_conditions.append("site_name LIKE %s")
                params.append(f"%{filters['site_name']}%")
            if filters.get('province'):
                where_conditions.append("province = %s")
                params.append(filters['province'])
            if filters.get('city'):
                where_conditions.append("city = %s")
                params.append(filters['city'])
            if filters.get('publish_status'):
                where_conditions.append("publish_status = %s")
                params.append(filters['publish_status'])
            if filters.get('start_date'):
                where_conditions.append("DATE(collected_at) >= %s")
                params.append(filters['start_date'])
            if filters.get('end_date'):
                where_conditions.append("DATE(collected_at) <= %s")
                params.append(filters['end_date'])
            if filters.get('keyword'):
                where_conditions.append("(bid_title LIKE %s OR bid_content LIKE %s)")
                keyword = f"%{filters['keyword']}%"
                params.extend([keyword, keyword])
        
        where_clause = " AND ".join(where_conditions)
        
        # 查询总数
        count_sql = f"SELECT COUNT(*) as total FROM collection_results WHERE {where_clause}"
        count_result = db.execute_query(count_sql, params)
        total = count_result[0]['total'] if count_result else 0
        
        # 查询数据
        sql = f"""
        SELECT id, site_name, site_url, province, city, bid_url, bid_title, 
               publish_status, collected_at, created_at
        FROM collection_results 
        WHERE {where_clause}
        ORDER BY collected_at DESC 
        LIMIT %s OFFSET %s
        """
        params.extend([page_size, offset])
        
        results = db.execute_query(sql, params)
        
        return {
            'data': results,
            'total': total,
            'page': page,
            'page_size': page_size,
            'total_pages': (total + page_size - 1) // page_size
        }
    
    @staticmethod
    def get_by_id(result_id):
        """根据ID获取采集结果"""
        sql = "SELECT * FROM collection_results WHERE id = %s"
        result = db.execute_query(sql, [result_id])
        return result[0] if result else None
    
    @staticmethod
    def get_by_task_id(task_id, page=1, page_size=20):
        """根据任务ID获取采集结果"""
        offset = (page - 1) * page_size
        
        # 查询总数
        count_sql = "SELECT COUNT(*) as total FROM collection_results WHERE task_id = %s"
        count_result = db.execute_query(count_sql, [task_id])
        total = count_result[0]['total'] if count_result else 0
        
        # 查询数据
        sql = """
        SELECT * FROM collection_results 
        WHERE task_id = %s
        ORDER BY collected_at DESC 
        LIMIT %s OFFSET %s
        """
        results = db.execute_query(sql, [task_id, page_size, offset])
        
        return {
            'data': results,
            'total': total,
            'page': page,
            'page_size': page_size,
            'total_pages': (total + page_size - 1) // page_size
        }
    
    @staticmethod
    def create(data):
        """创建采集结果"""
        sql = """
        INSERT INTO collection_results (task_id, site_id, site_name, site_url, 
                                      province, city, bid_url, bid_title, bid_content)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = [
            data.get('task_id'),
            data.get('site_id'),
            data.get('site_name'),
            data.get('site_url'),
            data.get('province'),
            data.get('city'),
            data.get('bid_url'),
            data.get('bid_title'),
            data.get('bid_content')
        ]
        
        result = db.execute_update(sql, params)
        if result:
            return db.get_last_insert_id()
        return None
    
    @staticmethod
    def update_publish_status(result_id, publish_status):
        """更新发布状态"""
        sql = "UPDATE collection_results SET publish_status = %s WHERE id = %s"
        return db.execute_update(sql, [publish_status, result_id])
    
    @staticmethod
    def delete(result_id):
        """删除采集结果"""
        sql = "DELETE FROM collection_results WHERE id = %s"
        return db.execute_update(sql, [result_id])
