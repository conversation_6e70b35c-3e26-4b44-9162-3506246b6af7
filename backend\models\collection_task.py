from .database import db
from datetime import datetime, date

class CollectionTask:
    def __init__(self):
        pass
    
    @staticmethod
    def create(site_id):
        """创建采集任务"""
        sql = """
        INSERT INTO collection_tasks (site_id, task_status, start_time)
        VALUES (%s, 'pending', %s)
        """
        params = [site_id, datetime.now()]
        
        result = db.execute_update(sql, params)
        if result:
            return db.get_last_insert_id()
        return None
    
    @staticmethod
    def update_status(task_id, status, **kwargs):
        """更新任务状态"""
        update_fields = ["task_status = %s"]
        params = [status]
        
        if status == 'running':
            update_fields.append("start_time = %s")
            params.append(datetime.now())
        elif status in ['completed', 'failed']:
            update_fields.append("end_time = %s")
            params.append(datetime.now())
        
        if 'total_links' in kwargs:
            update_fields.append("total_links = %s")
            params.append(kwargs['total_links'])
        
        if 'success_count' in kwargs:
            update_fields.append("success_count = %s")
            params.append(kwargs['success_count'])
        
        if 'failed_count' in kwargs:
            update_fields.append("failed_count = %s")
            params.append(kwargs['failed_count'])
        
        if 'error_message' in kwargs:
            update_fields.append("error_message = %s")
            params.append(kwargs['error_message'])
        
        params.append(task_id)
        
        sql = f"UPDATE collection_tasks SET {', '.join(update_fields)} WHERE id = %s"
        return db.execute_update(sql, params)
    
    @staticmethod
    def get_by_id(task_id):
        """根据ID获取任务"""
        sql = "SELECT * FROM collection_tasks WHERE id = %s"
        result = db.execute_query(sql, [task_id])
        return result[0] if result else None
    
    @staticmethod
    def get_by_site_id(site_id, limit=10):
        """根据站点ID获取任务列表"""
        sql = """
        SELECT * FROM collection_tasks 
        WHERE site_id = %s 
        ORDER BY created_at DESC 
        LIMIT %s
        """
        return db.execute_query(sql, [site_id, limit])
    
    @staticmethod
    def get_today_statistics():
        """获取今日统计"""
        today = date.today()
        
        # 今日任务统计
        sql = """
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN task_status = 'completed' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN task_status = 'failed' THEN 1 ELSE 0 END) as failed,
            SUM(CASE WHEN task_status = 'running' THEN 1 ELSE 0 END) as running
        FROM collection_tasks 
        WHERE DATE(created_at) = %s
        """
        result = db.execute_query(sql, [today])
        stats = result[0] if result else {}
        
        # 获取总站点数
        total_sites_sql = "SELECT COUNT(*) as total FROM bid_sites WHERE status = 1"
        total_result = db.execute_query(total_sites_sql)
        total_sites = total_result[0]['total'] if total_result else 0
        
        return {
            'today_collected': stats.get('completed', 0),
            'today_uncollected': total_sites - stats.get('total', 0),
            'today_success': stats.get('completed', 0),
            'today_failed': stats.get('failed', 0),
            'today_running': stats.get('running', 0)
        }
