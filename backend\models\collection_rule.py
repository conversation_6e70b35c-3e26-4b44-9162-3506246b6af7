from .database import db

class CollectionRule:
    def __init__(self):
        pass
    
    @staticmethod
    def get_by_site_id(site_id):
        """根据站点ID获取采集规则"""
        sql = "SELECT * FROM collection_rules WHERE site_id = %s"
        result = db.execute_query(sql, [site_id])
        return result[0] if result else None
    
    @staticmethod
    def create_or_update(site_id, data):
        """创建或更新采集规则"""
        # 先检查是否已存在
        existing = CollectionRule.get_by_site_id(site_id)
        
        if existing:
            # 更新
            sql = """
            UPDATE collection_rules 
            SET link_extract_type=%s, link_extract_rule=%s,
                title_extract_type=%s, title_extract_rule=%s,
                content_extract_type=%s, content_extract_rule=%s
            WHERE site_id=%s
            """
            params = [
                data.get('link_extract_type'),
                data.get('link_extract_rule'),
                data.get('title_extract_type'),
                data.get('title_extract_rule'),
                data.get('content_extract_type'),
                data.get('content_extract_rule'),
                site_id
            ]
            return db.execute_update(sql, params)
        else:
            # 创建
            sql = """
            INSERT INTO collection_rules (site_id, link_extract_type, link_extract_rule,
                                        title_extract_type, title_extract_rule,
                                        content_extract_type, content_extract_rule)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            params = [
                site_id,
                data.get('link_extract_type'),
                data.get('link_extract_rule'),
                data.get('title_extract_type'),
                data.get('title_extract_rule'),
                data.get('content_extract_type'),
                data.get('content_extract_rule')
            ]
            result = db.execute_update(sql, params)
            if result:
                return db.get_last_insert_id()
            return None
    
    @staticmethod
    def delete_by_site_id(site_id):
        """根据站点ID删除采集规则"""
        sql = "DELETE FROM collection_rules WHERE site_id = %s"
        return db.execute_update(sql, [site_id])
