#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
招标采集脚本
支持re、xpath、bs4三种提取方式
"""

import requests
import re
import time
import random
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from lxml import html, etree
import logging
from typing import List, Dict, Optional, Tuple

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SpiderExtractor:
    """采集提取器"""
    
    def __init__(self, timeout=30, max_retries=3):
        self.timeout = timeout
        self.max_retries = max_retries
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
    
    def get_page_content(self, url: str) -> Optional[str]:
        """获取页面内容"""
        for attempt in range(self.max_retries):
            try:
                # 随机延迟，避免被反爬
                time.sleep(random.uniform(1, 3))
                
                response = self.session.get(url, timeout=self.timeout)
                response.raise_for_status()
                
                # 尝试检测编码
                if response.encoding == 'ISO-8859-1':
                    # 尝试从content中检测编码
                    content = response.content
                    if b'charset=utf-8' in content or b'charset="utf-8"' in content:
                        response.encoding = 'utf-8'
                    elif b'charset=gbk' in content or b'charset="gbk"' in content:
                        response.encoding = 'gbk'
                    elif b'charset=gb2312' in content or b'charset="gb2312"' in content:
                        response.encoding = 'gb2312'
                
                return response.text
                
            except requests.RequestException as e:
                logger.warning(f"获取页面失败 (尝试 {attempt + 1}/{self.max_retries}): {url}, 错误: {e}")
                if attempt == self.max_retries - 1:
                    logger.error(f"获取页面最终失败: {url}")
                    return None
        
        return None
    
    def extract_by_regex(self, content: str, pattern: str) -> List[str]:
        """使用正则表达式提取"""
        try:
            matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
            if isinstance(matches[0], tuple):
                # 如果有分组，返回第一个分组
                return [match[0] if match else '' for match in matches]
            return matches
        except Exception as e:
            logger.error(f"正则表达式提取失败: {e}")
            return []
    
    def extract_by_xpath(self, content: str, xpath: str) -> List[str]:
        """使用XPath提取"""
        try:
            tree = html.fromstring(content)
            elements = tree.xpath(xpath)
            
            results = []
            for element in elements:
                if isinstance(element, str):
                    results.append(element.strip())
                elif hasattr(element, 'text_content'):
                    results.append(element.text_content().strip())
                elif hasattr(element, 'text'):
                    results.append((element.text or '').strip())
                else:
                    results.append(str(element).strip())
            
            return results
        except Exception as e:
            logger.error(f"XPath提取失败: {e}")
            return []
    
    def extract_by_bs4(self, content: str, selector: str) -> List[str]:
        """使用BeautifulSoup4提取"""
        try:
            soup = BeautifulSoup(content, 'html.parser')
            
            # 解析选择器
            if selector.startswith('css:'):
                # CSS选择器
                css_selector = selector[4:].strip()
                elements = soup.select(css_selector)
            else:
                # 标签选择器（简单解析）
                parts = selector.split('.')
                tag = parts[0] if parts[0] else None
                class_name = parts[1] if len(parts) > 1 else None
                
                if tag and class_name:
                    elements = soup.find_all(tag, class_=class_name)
                elif tag:
                    elements = soup.find_all(tag)
                elif class_name:
                    elements = soup.find_all(class_=class_name)
                else:
                    elements = []
            
            results = []
            for element in elements:
                if hasattr(element, 'get_text'):
                    results.append(element.get_text().strip())
                elif hasattr(element, 'text'):
                    results.append(element.text.strip())
                else:
                    results.append(str(element).strip())
            
            return results
        except Exception as e:
            logger.error(f"BeautifulSoup4提取失败: {e}")
            return []
    
    def extract_links(self, url: str, extract_type: str, extract_rule: str) -> Dict:
        """提取链接"""
        try:
            content = self.get_page_content(url)
            if not content:
                return {'links': [], 'error': '无法获取页面内容'}
            
            # 根据提取方式进行提取
            if extract_type == 're':
                raw_links = self.extract_by_regex(content, extract_rule)
            elif extract_type == 'xpath':
                raw_links = self.extract_by_xpath(content, extract_rule)
            elif extract_type == 'bs4':
                raw_links = self.extract_by_bs4(content, extract_rule)
            else:
                return {'links': [], 'error': f'不支持的提取方式: {extract_type}'}
            
            # 处理链接，转换为绝对URL
            links = []
            for link in raw_links:
                if link:
                    # 如果是相对链接，转换为绝对链接
                    absolute_link = urljoin(url, link.strip())
                    if absolute_link not in links:  # 去重
                        links.append(absolute_link)
            
            return {'links': links, 'html': content, 'error': None}
            
        except Exception as e:
            logger.error(f"提取链接失败: {e}")
            return {'links': [], 'html': '', 'error': str(e)}
    
    def extract_content(self, url: str, title_type: str, title_rule: str, 
                       content_type: str, content_rule: str) -> Dict:
        """提取标题和内容"""
        try:
            page_content = self.get_page_content(url)
            if not page_content:
                return {'title': '', 'content': '', 'error': '无法获取页面内容'}
            
            # 提取标题
            if title_type == 're':
                titles = self.extract_by_regex(page_content, title_rule)
            elif title_type == 'xpath':
                titles = self.extract_by_xpath(page_content, title_rule)
            elif title_type == 'bs4':
                titles = self.extract_by_bs4(page_content, title_rule)
            else:
                return {'title': '', 'content': '', 'error': f'不支持的标题提取方式: {title_type}'}
            
            title = titles[0] if titles else ''
            
            # 提取内容
            if content_type == 're':
                contents = self.extract_by_regex(page_content, content_rule)
            elif content_type == 'xpath':
                contents = self.extract_by_xpath(page_content, content_rule)
            elif content_type == 'bs4':
                contents = self.extract_by_bs4(page_content, content_rule)
            else:
                return {'title': title, 'content': '', 'error': f'不支持的内容提取方式: {content_type}'}
            
            content = contents[0] if contents else ''

            return {
                'title': title.strip(),
                'content': content.strip(),
                'html': page_content,  # 添加HTML源码
                'error': None
            }
            
        except Exception as e:
            logger.error(f"提取内容失败: {e}")
            return {'title': '', 'content': '', 'error': str(e)}

# 全局提取器实例
extractor = SpiderExtractor()

def test_link_extraction(url: str, extract_type: str, extract_rule: str) -> Dict:
    """测试链接提取"""
    return extractor.extract_links(url, extract_type, extract_rule)

def test_content_extraction(url: str, title_type: str, title_rule: str, 
                          content_type: str, content_rule: str) -> Dict:
    """测试内容提取"""
    return extractor.extract_content(url, title_type, title_rule, content_type, content_rule)

def test_full_extraction(list_url: str, link_type: str, link_rule: str,
                        title_type: str, title_rule: str,
                        content_type: str, content_rule: str, max_links: int = 5) -> Dict:
    """测试完整采集流程"""
    try:
        # 第一步：提取链接
        link_result = extractor.extract_links(list_url, link_type, link_rule)
        if link_result['error']:
            return {'error': f'链接提取失败: {link_result["error"]}', 'results': []}
        
        links = link_result['links'][:max_links]  # 限制测试链接数量
        
        if not links:
            return {'error': '未提取到任何链接', 'results': []}
        
        # 第二步：提取每个链接的内容
        results = []
        for i, link in enumerate(links):
            logger.info(f"正在处理第 {i+1}/{len(links)} 个链接: {link}")
            
            content_result = extractor.extract_content(
                link, title_type, title_rule, content_type, content_rule
            )
            
            result_item = {
                'url': link,
                'title': content_result['title'],
                'content': content_result['content'],
                'error': content_result['error']
            }
            results.append(result_item)
        
        return {
            'total_links': len(link_result['links']),
            'processed_links': len(results),
            'results': results,
            'error': None
        }
        
    except Exception as e:
        logger.error(f"完整采集测试失败: {e}")
        return {'error': str(e), 'results': []}

if __name__ == '__main__':
    # 测试代码
    print("招标采集脚本已启动")
    
    # 示例测试
    test_url = "http://www.ccgp.gov.cn/"
    test_link_rule = r'href="([^"]*)"'
    
    result = test_link_extraction(test_url, 're', test_link_rule)
    print(f"测试结果: {len(result['links'])} 个链接")
    for link in result['links'][:5]:
        print(f"  - {link}")
